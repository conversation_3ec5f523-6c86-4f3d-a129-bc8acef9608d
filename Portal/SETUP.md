# QuantomRhino Portal Setup Guide

## 🚀 Quick Start

### 1. Azure AD Application Setup

Before deploying, you need to create an Azure AD application:

1. Go to [Azure Portal](https://portal.azure.com)
2. Navigate to **Azure Active Directory** > **App registrations**
3. Click **New registration**
4. Fill out the form:
   - Name: `QuantomRhino Portal`
   - Account types: `Accounts in this organizational directory only`
   - Redirect URI: **IMPORTANT**: Select `Single-page application (SPA)` + `https://qr-portal-production.up.railway.app`
5. Click **Register**
6. Note down the **Application (client) ID** and **Directory (tenant) ID**
7. Go to **Certificates & secrets** > **New client secret**
8. Create a secret and note down the **Value**

**⚠️ CRITICAL: Make sure you select "Single-page application (SPA)" as the platform type, not "Web"!**

### Additional Configuration Steps:

9. Go to **Authentication** in your Azure AD app
10. Under **Platform configurations**, ensure you have:
    - Platform: `Single-page application`
    - Redirect URIs: `https://qr-portal-production.up.railway.app`
11. Under **Implicit grant and hybrid flows**, enable:
    - ✅ Access tokens (used for implicit flows)
    - ✅ ID tokens (used for implicit and hybrid flows)
12. Click **Save**

### 2. Railway Deployment

#### Option A: Deploy with Railway CLI
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Create new project
railway init

# Deploy
railway up 
```

#### Option B: Deploy via GitHub Integration
1. Connect your GitHub repository to Railway
2. Railway will automatically detect this is a Next.js project
3. Set environment variables (see below)
4. Deploy automatically on push

### 3. Environment Variables

Set these in Railway dashboard or via CLI:

```bash
# Database (Railway will provide this automatically if you add PostgreSQL)
DATABASE_URL="postgresql://username:password@host:port/database"

# Azure AD Configuration
NEXT_PUBLIC_AZURE_AD_CLIENT_ID="your-client-id"
NEXT_PUBLIC_AZURE_AD_TENANT_ID="your-tenant-id"
AZURE_AD_CLIENT_SECRET="your-client-secret"
NEXT_PUBLIC_REDIRECT_URI="https://your-app.railway.app"

# NextAuth
NEXTAUTH_URL="https://your-app.railway.app"
NEXTAUTH_SECRET="generate-a-random-32-character-string"

# App Configuration
NEXT_PUBLIC_APP_NAME="QuantomRhino Portal"
NEXT_PUBLIC_COMPANY_NAME="QuantomRhino"
```

### 4. Add PostgreSQL Database

In Railway dashboard:
1. Go to your project
2. Click **+ New**
3. Select **Database** > **PostgreSQL**
4. Railway will automatically set `DATABASE_URL`

### 5. Run Database Migrations

After deployment, run migrations:
```bash
# First, make sure you're connected to your app service (not the database service)
railway service QR-Portal

# Then run the migration
railway run npx prisma db push
```

**⚠️ Troubleshooting Database Connection Issues:**

If you get a `P1001: Can't reach database server` error:

1. **Check your DATABASE_URL is set correctly:**
   ```bash
   railway variables
   ```

2. **If DATABASE_URL is placeholder text, set it manually:**
   ```bash
   # Switch to Postgres service to get the public URL
   railway service Postgres-Utvb
   railway variables | grep DATABASE_PUBLIC_URL
   
   # Switch back to your app service
   railway service QR-Portal
   
   # Set the DATABASE_URL using the public URL (replace with your actual values)
   railway variables --set DATABASE_URL="postgresql://postgres:<EMAIL>:YOUR_PORT/railway"
   ```

3. **Common issues:**
   - Make sure you're running commands from the **app service** (QR-Portal), not the database service
   - Use the `DATABASE_PUBLIC_URL` (with gondola.proxy.rlwy.net) for external connections
   - The internal URLs (railway.internal) only work within Railway's infrastructure

### 6. Update Azure AD Redirect URI

After deployment:
1. **Get your Railway app URL**: Your app is deployed at `https://qr-portal-production.up.railway.app`
2. **Update Azure AD app registration**:
   - Go to [Azure Portal](https://portal.azure.com) 
   - Navigate to **Azure Active Directory** > **App registrations**
   - Select your app: `QuantomRhino Portal`
   - Go to **Authentication** > **Platform configurations** > **Web**
   - Update redirect URI to: `https://qr-portal-production.up.railway.app`
   - Click **Save**

**✅ Your portal is now live at: https://qr-portal-production.up.railway.app**

## 🔧 Local Development

### Prerequisites
- Node.js 18+
- PostgreSQL database
- Azure AD application (see setup above)

### Steps
1. Clone the repository
2. Install dependencies: `npm install`
3. Copy `.env.example` to `.env.local`
4. Update environment variables
5. Generate Prisma client: `npm run db:generate`
6. Push database schema: `npm run db:push`
7. Start development server: `npm run dev`

## 🐳 Docker Integration

The portal is designed to manage containerized applications. To enable full Docker functionality:

### Local Development with Docker
```bash
# Make sure Docker is running
docker --version

# The app will connect to Docker socket at unix:///var/run/docker.sock
```

### Production Docker Management
For production, you'll need to configure Docker access based on your infrastructure:

1. **Railway with Docker**: Contact Railway support for Docker socket access
2. **Custom Infrastructure**: Ensure Docker API is accessible
3. **Kubernetes**: Use Kubernetes API instead of Docker API

## 🔒 Security Features

- **MSAL Authentication**: Secure Microsoft Azure AD integration
- **CSRF Protection**: Built-in Next.js CSRF protection
- **Secure Headers**: Configured via next.config.js
- **Input Validation**: All API endpoints validate inputs
- **Environment Isolation**: Secure environment variable handling

## 📝 Usage

### Managing Applications
1. **Deploy New App**: Click "Deploy New App" button
2. **View Applications**: See all apps on the dashboard
3. **Start/Stop/Restart**: Use action buttons on each app card
4. **Monitor**: View deployment status and logs

### User Management
- Users authenticate via Microsoft Azure AD
- Role-based access control (Admin, User, Viewer)
- Automatic user provisioning on first login

### Monitoring
- Real-time application status
- Deployment history and logs
- Performance metrics and analytics

## 🛠️ Customization

### Adding New Features
1. Create new components in `components/`
2. Add API routes in `app/api/`
3. Update database schema in `prisma/schema.prisma`
4. Run `npm run db:push` to apply changes

### Styling
- Uses Tailwind CSS for styling
- Dark/light mode support
- Responsive design
- Customizable via `tailwind.config.js`

## 🚨 Troubleshooting

### Common Issues

1. **Build Errors**: Check TypeScript errors and missing dependencies
2. **Database Connection**: Verify DATABASE_URL is correct
3. **Authentication Issues**: Check Azure AD configuration
4. **Docker Access**: Ensure Docker daemon is running and accessible

### Azure AD Authentication Issues

**Error: "AADSTS900023: Specified tenant identifier 'undefined'"**

This error occurs when environment variables aren't properly loaded in production.

1. **Verify environment variables in Railway:**
   ```bash
   railway variables
   ```
   Ensure these are set:
   - `NEXT_PUBLIC_AZURE_AD_CLIENT_ID`
   - `NEXT_PUBLIC_AZURE_AD_TENANT_ID`
   - `NEXT_PUBLIC_REDIRECT_URI`

2. **Check browser console for MSAL debug logs:**
   - Open developer tools and look for "MSAL Config Debug" or "Initializing MSAL with" messages
   - This will show if environment variables are being loaded correctly

3. **Verify Azure AD app registration:**
   - Make sure redirect URI is set to your Railway app URL
   - Confirm client ID and tenant ID match Railway environment variables

4. **Common fixes:**
   - Redeploy after setting environment variables
   - Clear browser cache and try again
   - Check that environment variables don't have extra spaces or quotes

**Error: "Cross-origin token redemption is permitted only for the 'Single-Page Application' client-type"**

This error occurs when the Azure AD app registration is not configured as a Single-Page Application.

1. **Fix Azure AD app registration:**
   - Go to [Azure Portal](https://portal.azure.com) > **Azure Active Directory** > **App registrations**
   - Select your app: `QuantomRhino Portal`
   - Go to **Authentication**
   - Under **Platform configurations**, if you see "Web" platform, remove it
   - Click **Add a platform** > **Single-page application**
   - Add redirect URI: `https://qr-portal-production.up.railway.app`
   - Under **Implicit grant and hybrid flows**, enable:
     - ✅ Access tokens (used for implicit flows)
     - ✅ ID tokens (used for implicit and hybrid flows)
   - Click **Save**

2. **Verify the configuration:**
   - Ensure Platform shows "Single-page application" not "Web"
   - Confirm redirect URI matches your Railway app URL exactly
   - Check that implicit grant tokens are enabled

### Support

For technical support:
1. Check the logs in Railway dashboard
2. Review browser console for client-side errors
3. Verify environment variables are set correctly
4. Check Azure AD application configuration

## 📚 Documentation

- [Next.js Documentation](https://nextjs.org/docs)
- [Railway Documentation](https://docs.railway.app)
- [Azure AD Documentation](https://docs.microsoft.com/en-us/azure/active-directory)
- [Prisma Documentation](https://www.prisma.io/docs)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
