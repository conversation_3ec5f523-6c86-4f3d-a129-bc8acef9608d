# Environment Variables Template
# Copy this file to .env.local and update with your actual values

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/portal_db"

# Azure AD/MSAL Configuration
NEXT_PUBLIC_AZURE_AD_CLIENT_ID="your-azure-ad-client-id"
NEXT_PUBLIC_AZURE_AD_TENANT_ID="your-azure-ad-tenant-id"
AZURE_AD_CLIENT_SECRET="your-azure-ad-client-secret"
NEXT_PUBLIC_REDIRECT_URI="http://localhost:3000"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-generate-a-random-string"

# Docker Configuration
DOCKER_HOST="unix:///var/run/docker.sock"
DOCKER_REGISTRY_URL="your-docker-registry-url"

# App Configuration
NEXT_PUBLIC_APP_NAME="QuantomRhino Portal"
NEXT_PUBLIC_APP_VERSION="1.0.0"
NEXT_PUBLIC_COMPANY_NAME="QuantomRhino"

# Railway Production Overrides (set in Railway dashboard)
# DATABASE_URL will be provided by Railway PostgreSQL
# NEXT_PUBLIC_REDIRECT_URI should be your production URL
