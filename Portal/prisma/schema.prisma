generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl", "debian-openssl-1.1.x", "debian-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id                String             @id @default(cuid())
  email             String             @unique
  name              String?
  avatar            String?
  role              Role               @default(USER)
  adminLevel        Int                @default(0)
  tenantId          String?
  organizationId    String?
  isActive          Boolean            @default(true)
  createdAt         DateTime           @default(now())
  updatedAt         DateTime           @updatedAt
  lastLoginAt       DateTime?
  applications      Application[]
  auditLogs         AuditLog[]
  deployments       Deployment[]
  sessions          Session[]
  organization      Organization?      @relation(fields: [organizationId], references: [id])
  webAppPermissions WebAppPermission[]
  createdWebApps    WebApp[]           @relation("WebAppCreator")

  @@map("users")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  description String?
  logo        String?
  domain      String?  @unique
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]
  webApps     WebApp[]

  @@map("organizations")
}

model WebApp {
  id             String             @id @default(cuid())
  name           String
  title          String
  description    String?
  image          String?
  url            String
  category       String?
  tags           String[]
  isActive       Boolean            @default(true)
  requiresAuth   Boolean            @default(true)
  organizationId String?
  createdById    String
  order          Int                @default(0)
  createdAt      DateTime           @default(now())
  updatedAt      DateTime           @updatedAt
  permissions    WebAppPermission[]
  createdBy      User               @relation("WebAppCreator", fields: [createdById], references: [id])
  organization   Organization?      @relation(fields: [organizationId], references: [id])

  @@map("webapps")
}

model WebAppPermission {
  id          String   @id @default(cuid())
  userId      String
  webAppId    String
  canAccess   Boolean  @default(true)
  grantedAt   DateTime @default(now())
  grantedById String?
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  webApp      WebApp   @relation(fields: [webAppId], references: [id], onDelete: Cascade)

  @@unique([userId, webAppId])
  @@map("webapp_permissions")
}

model AuditLog {
  id        String   @id @default(cuid())
  action    String
  details   Json?
  userId    String?
  targetId  String?
  createdAt DateTime @default(now())
  user      User?    @relation(fields: [userId], references: [id])

  @@map("audit_logs")
}

model Application {
  id          String            @id @default(cuid())
  name        String
  description String?
  dockerImage String
  port        Int               @default(3000)
  status      ApplicationStatus @default(STOPPED)
  url         String?
  repository  String?
  branch      String?           @default("main")
  category    String?
  tags        String[]
  config      Json?
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt
  ownerId     String
  owner       User              @relation(fields: [ownerId], references: [id], onDelete: Cascade)
  deployments Deployment[]

  @@map("applications")
}

model Deployment {
  id            String           @id @default(cuid())
  version       String
  status        DeploymentStatus @default(PENDING)
  containerId   String?
  logs          String?
  startedAt     DateTime?
  completedAt   DateTime?
  errorMessage  String?
  createdAt     DateTime         @default(now())
  updatedAt     DateTime         @updatedAt
  applicationId String
  deployedById  String
  application   Application      @relation(fields: [applicationId], references: [id], onDelete: Cascade)
  deployedBy    User             @relation(fields: [deployedById], references: [id])

  @@map("deployments")
}

model Session {
  id        String   @id @default(cuid())
  token     String   @unique
  userId    String
  expiresAt DateTime
  createdAt DateTime @default(now())
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

enum Role {
  SUPER_ADMIN
  ORG_ADMIN
  USER
  VIEWER
}

enum ApplicationStatus {
  RUNNING
  STOPPED
  STARTING
  STOPPING
  ERROR
}

enum DeploymentStatus {
  PENDING
  BUILDING
  DEPLOYING
  SUCCESS
  FAILED
}
