{"name": "quantom-rhino-portal", "version": "1.0.0", "description": "Secure web app portal for containerized applications", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "node server.js", "start:next": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "dependencies": {"@azure/msal-browser": "^3.11.1", "@azure/msal-react": "^2.0.15", "@prisma/client": "^5.10.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@tanstack/react-query": "^5.24.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.356.0", "next": "14.1.3", "next-themes": "^0.2.1", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "zustand": "^4.5.2"}, "devDependencies": {"@types/dockerode": "^3.3.26", "@types/node": "^20.11.25", "@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-config-next": "14.1.3", "postcss": "^8.4.35", "prettier": "^3.2.5", "prettier-plugin-tailwindcss": "^0.5.11", "prisma": "^5.10.2", "tailwindcss": "^3.4.1", "typescript": "^5.4.2"}, "engines": {"node": ">=18.0.0"}}