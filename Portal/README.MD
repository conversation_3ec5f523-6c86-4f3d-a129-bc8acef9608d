# QuantomRhino Portal

A secure, modern web application portal for managing and accessing containerized web applications.

## Features

- 🔐 **Secure Authentication**: Microsoft Azure AD/Entra ID integration with MSAL
- 🎨 **Modern UI**: Beautiful, responsive design with Tailwind CSS and Shadcn/ui
- 🐳 **Container Management**: Docker integration for managing containerized apps
- 📊 **Dashboard**: Intuitive dashboard for easy app access and management
- 🔒 **Security First**: Built with Next.js 14, TypeScript, and security best practices
- ☁️ **Railway Ready**: Optimized for Railway deployment

## Tech Stack

- **Frontend**: Next.js 14, TypeScript, React 18
- **Authentication**: Microsoft Authentication Library (MSAL)
- **UI**: Tailwind CSS, Shadcn/ui, Framer Motion
- **Database**: PostgreSQL with Prisma ORM
- **Container Management**: Docker API integration
- **Deployment**: Railway

## Getting Started

### Prerequisites

- Node.js 18+ 
- Docker
- PostgreSQL database
- Azure AD/Entra ID application registration

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd Portal
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Configure your `.env.local` file with:
- Azure AD application details
- Database connection string
- Docker configuration

5. Set up the database:
```bash
npm run db:generate
npm run db:push
```

6. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the portal.

## Environment Variables

Create a `.env.local` file with the following variables:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/portal_db"

# Azure AD/MSAL Configuration
NEXT_PUBLIC_AZURE_AD_CLIENT_ID="your-client-id"
NEXT_PUBLIC_AZURE_AD_TENANT_ID="your-tenant-id"
AZURE_AD_CLIENT_SECRET="your-client-secret"
NEXT_PUBLIC_REDIRECT_URI="http://localhost:3000/auth/callback"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret"

# Docker Configuration
DOCKER_HOST="unix:///var/run/docker.sock"

# App Configuration
NEXT_PUBLIC_APP_NAME="QuantomRhino Portal"
NEXT_PUBLIC_APP_VERSION="1.0.0"
```

## Deployment

### Railway Deployment

1. Connect your repository to Railway
2. Add environment variables in Railway dashboard
3. Railway will automatically detect and deploy your Next.js application

### Docker Support

The application includes Docker configuration for containerized deployment:

```bash
# Build the image
docker build -t quantom-rhino-portal .

# Run the container
docker run -p 3000:3000 quantom-rhino-portal
```

## Security Features

- **MSAL Authentication**: Secure Azure AD integration
- **CSRF Protection**: Built-in Cross-Site Request Forgery protection
- **Secure Headers**: Security headers configured via Next.js
- **Role-Based Access**: User roles and permissions system
- **Input Validation**: Zod schema validation for all inputs
- **Environment Isolation**: Secure environment variable handling

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

Private - QuantomRhino Internal Use Only
