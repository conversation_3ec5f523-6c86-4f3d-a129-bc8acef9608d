'use client'

// Runtime environment variable loader for production
// This ensures environment variables are available even if they weren't embedded at build time

interface EnvironmentConfig {
  NEXT_PUBLIC_AZURE_AD_CLIENT_ID?: string
  NEXT_PUBLIC_AZURE_AD_TENANT_ID?: string
  NEXT_PUBLIC_REDIRECT_URI?: string
  NEXT_PUBLIC_APP_NAME?: string
  NEXT_PUBLIC_COMPANY_NAME?: string
}

let runtimeConfig: EnvironmentConfig | null = null

// Function to load environment variables at runtime from the server
export const loadRuntimeConfig = async (): Promise<EnvironmentConfig> => {
  if (runtimeConfig) {
    return runtimeConfig
  }
  try {
    // Try to fetch environment variables from a server endpoint
    const response = await fetch('/api/auth-config', { 
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache',
      }
    })
    if (response.ok) {
      const data = await response.json()
      runtimeConfig = data.environment
      console.log('Runtime config loaded from server:', {
        clientId: runtimeConfig?.NEXT_PUBLIC_AZURE_AD_CLIENT_ID ? 'loaded' : 'missing',
        tenantId: runtimeConfig?.NEXT_PUBLIC_AZURE_AD_TENANT_ID ? 'loaded' : 'missing',
        redirectUri: runtimeConfig?.NEXT_PUBLIC_REDIRECT_URI || 'using default',
        debug: data.debug
      })
      return runtimeConfig!
    }
  } catch (error) {
    console.warn('Failed to load runtime config from server:', error)
  }

  // Fallback to process.env (should work in development and proper builds)
  runtimeConfig = {
    NEXT_PUBLIC_AZURE_AD_CLIENT_ID: process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID,
    NEXT_PUBLIC_AZURE_AD_TENANT_ID: process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID,
    NEXT_PUBLIC_REDIRECT_URI: process.env.NEXT_PUBLIC_REDIRECT_URI,
    NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
    NEXT_PUBLIC_COMPANY_NAME: process.env.NEXT_PUBLIC_COMPANY_NAME,
  }

  console.log('Runtime config loaded from process.env:', {
    clientId: runtimeConfig?.NEXT_PUBLIC_AZURE_AD_CLIENT_ID ? 'loaded' : 'missing',
    tenantId: runtimeConfig?.NEXT_PUBLIC_AZURE_AD_TENANT_ID ? 'loaded' : 'missing',
    redirectUri: runtimeConfig?.NEXT_PUBLIC_REDIRECT_URI || 'using default',
  })

  return runtimeConfig
}

// Get a specific environment variable with fallback
export const getEnvVar = async (key: keyof EnvironmentConfig): Promise<string | undefined> => {
  const config = await loadRuntimeConfig()
  return config[key]
}

// Reset the runtime config (useful for testing)
export const resetRuntimeConfig = () => {
  runtimeConfig = null
}
