'use client'

import { PublicClientApplication } from '@azure/msal-browser'
import { loadRuntimeConfig } from '@/lib/runtime-config'

let msalInstance: PublicClientApplication | null = null

export const initializeMsal = async () => {
  if (typeof window === 'undefined') {
    return null
  }

  if (msalInstance) {
    return msalInstance
  }

  try {
    // Load configuration at runtime
    const config = await loadRuntimeConfig()
    
    const clientId = config.NEXT_PUBLIC_AZURE_AD_CLIENT_ID
    const tenantId = config.NEXT_PUBLIC_AZURE_AD_TENANT_ID
    const redirectUri = config.NEXT_PUBLIC_REDIRECT_URI || window.location.origin

    console.log('Initializing MSAL with runtime config:', {
      clientId: clientId ? `${clientId.substring(0, 8)}...` : 'undefined',
      tenantId: tenantId ? `${tenantId.substring(0, 8)}...` : 'undefined',
      redirectUri,
    })

    if (!clientId) {
      throw new Error('NEXT_PUBLIC_AZURE_AD_CLIENT_ID is not defined')
    }

    if (!tenantId) {
      throw new Error('NEXT_PUBLIC_AZURE_AD_TENANT_ID is not defined')
    }    msalInstance = new PublicClientApplication({
      auth: {
        clientId,
        authority: `https://login.microsoftonline.com/${tenantId}`,
        redirectUri,
      },
      cache: {
        cacheLocation: 'sessionStorage',
        storeAuthStateInCookie: false,
      },
    })

    // Initialize the MSAL instance
    await msalInstance.initialize()

    console.log('MSAL instance initialized successfully')
    return msalInstance
  } catch (error) {
    console.error('Failed to initialize MSAL:', error)
    throw error
  }
}

export const getMsalInstance = async () => {
  return msalInstance || await initializeMsal()
}

export const loginRequest = {
  scopes: ['User.Read', 'email', 'profile', 'openid'],
}

export const graphConfig = {
  graphMeEndpoint: 'https://graph.microsoft.com/v1.0/me',
}
