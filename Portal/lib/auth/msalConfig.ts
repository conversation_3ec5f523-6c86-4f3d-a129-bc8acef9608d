import { Configuration, PublicClientApplication } from '@azure/msal-browser'

// Debug environment variables
console.log('MSAL Config Debug:', {
  clientId: process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID,
  tenantId: process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID,
  redirectUri: process.env.NEXT_PUBLIC_REDIRECT_URI,
})

// Validate required environment variables
const clientId = process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID
const tenantId = process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID
const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_URI || 'http://localhost:3000'

if (!clientId) {
  throw new Error('NEXT_PUBLIC_AZURE_AD_CLIENT_ID is not defined')
}

if (!tenantId) {
  throw new Error('NEXT_PUBLIC_AZURE_AD_TENANT_ID is not defined')
}

const msalConfig: Configuration = {
  auth: {
    clientId,
    authority: `https://login.microsoftonline.com/${tenantId}`,
    redirectUri,
  },
  cache: {
    cacheLocation: 'sessionStorage',
    storeAuthStateInCookie: false,
  },
}

export const msalInstance = new PublicClientApplication(msalConfig)

export const loginRequest = {
  scopes: ['User.Read', 'email', 'profile', 'openid'],
}

export const graphConfig = {
  graphMeEndpoint: 'https://graph.microsoft.com/v1.0/me',
}
