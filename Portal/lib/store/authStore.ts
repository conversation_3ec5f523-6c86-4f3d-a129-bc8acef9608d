import { AccountInfo } from '@azure/msal-browser'
import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  email: string
  name: string
  avatar?: string
  role: 'SUPER_ADMIN' | 'ORG_ADMIN' | 'USER' | 'VIEWER'
  adminLevel: number
  tenantId?: string
  organizationId?: string
  isActive: boolean
}

interface AuthState {
  user: User | null
  account: AccountInfo | null
  isAuthenticated: boolean
  isLoading: boolean
  setUser: (user: User | null) => void
  setAccount: (account: AccountInfo | null) => void
  setLoading: (loading: boolean) => void
  createOrUpdateUser: (account: AccountInfo) => Promise<User | null>
  refreshUser: () => Promise<void>
  logout: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      account: null,
      isAuthenticated: false,
      isLoading: true,
      setUser: (user) => set({ user, isAuthenticated: !!user }),
      setAccount: (account) => set({ account }),
      setLoading: (isLoading) => set({ isLoading }),
      createOrUpdateUser: async (account: AccountInfo) => {
        try {
          console.log('Creating/updating user from account:', account)
          
          const userData = {
            email: account.username,
            name: account.name || account.username.split('@')[0],
            tenantId: account.tenantId
          }

          const response = await fetch('/api/user-api', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(userData)
          })

          if (!response.ok) {
            console.error('Failed to create/update user:', response.status)
            return null
          }

          const user = await response.json()
          console.log('User created/updated:', user)
            set({ user, isAuthenticated: true })
          return user
        } catch (error) {
          console.error('Error creating/updating user:', error)
          return null
        }
      },
      refreshUser: async () => {
        const { user: currentUser } = get()
        if (!currentUser?.email) return

        try {
          console.log('Refreshing user data for:', currentUser.email)
          const response = await fetch('/api/user-api', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              email: currentUser.email,
              name: currentUser.name
            })
          })

          if (response.ok) {
            const updatedUser = await response.json()
            console.log('User data refreshed:', updatedUser)
            set({ user: updatedUser })
          }
        } catch (error) {
          console.error('Error refreshing user:', error)
        }
      },
      logout: () => set({ user: null, account: null, isAuthenticated: false }),
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ user: state.user, isAuthenticated: state.isAuthenticated }),
    }
  )
)
