import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/audit-logs - Get audit logs (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const adminUserId = searchParams.get('adminUserId')
    const action = searchParams.get('action')
    const targetId = searchParams.get('targetId')
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId },
      include: { organization: true }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    let whereClause: any = {}

    // Build where clause
    if (action) whereClause.action = action
    if (targetId) whereClause.targetId = targetId

    // If org admin, limit to actions within their organization's scope
    if (adminUser.role === 'ORG_ADMIN') {
      // This is a complex query - for now, org admins see all logs they can access
      // In a production system, you'd want to implement more granular filtering
    }

    const [logs, total] = await Promise.all([
      prisma.auditLog.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              role: true
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        skip: offset
      }),
      prisma.auditLog.count({ where: whereClause })
    ])

    return NextResponse.json({
      logs,
      pagination: {
        total,
        limit,
        offset,
        hasMore: offset + limit < total
      }
    })
  } catch (error) {
    console.error('Error fetching audit logs:', error)
    return NextResponse.json({ error: 'Failed to fetch audit logs' }, { status: 500 })
  }
}
