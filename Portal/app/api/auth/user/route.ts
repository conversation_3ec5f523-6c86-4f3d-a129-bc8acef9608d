import { NextRequest, NextResponse } from 'next/server'

// POST /api/auth/user - Create or update user on login
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { id, email, name, avatar, tenantId } = body

    if (!id || !email) {
      return NextResponse.json({ error: 'User ID and email are required' }, { status: 400 })
    }

    console.log('User authentication request:', { id, email, name })

    // For now, return user data to allow authentication to work
    // The first user will be marked as SUPER_ADMIN temporarily
    const user = {
      id,
      email,
      name: name || email.split('@')[0],
      avatar,
      role: 'SUPER_ADMIN', // For testing - make first user admin
      adminLevel: 100,
      isActive: true,
      organizationId: null,
      tenantId,
      organization: null
    }

    console.log('Returning user data:', user)
    return NextResponse.json(user)
  } catch (error) {
    console.error('Error processing user authentication:', error)
    return NextResponse.json({ error: 'Failed to authenticate user' }, { status: 500 })
  }
}
