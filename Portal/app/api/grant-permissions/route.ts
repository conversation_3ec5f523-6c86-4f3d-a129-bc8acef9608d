import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, webappId } = body

    if (!userId || !webappId) {
      return NextResponse.json({ error: 'userId and webappId required' }, { status: 400 })
    }

    // Check if permission already exists
    const existing = await prisma.webAppPermission.findFirst({
      where: {
        userId,
        webAppId: webappId
      }
    })

    if (existing) {
      return NextResponse.json({ message: 'Permission already exists', permission: existing })
    }

    // Create permission
    const permission = await prisma.webAppPermission.create({
      data: {
        userId,
        webAppId: webappId,
        canAccess: true
      }
    })

    return NextResponse.json({ 
      message: 'Permission created successfully', 
      permission 
    })

  } catch (error) {
    console.error('Error creating permission:', error)
    return NextResponse.json({ error: 'Failed to create permission' }, { status: 500 })
  }
}

// GET - auto-grant permissions for all users to all webapps (for testing)
export async function GET() {
  try {
    // Get all users and webapps
    const users = await prisma.user.findMany()
    const webapps = await prisma.webApp.findMany()

    const permissions = []

    for (const user of users) {
      for (const webapp of webapps) {
        // Check if permission already exists
        const existing = await prisma.webAppPermission.findFirst({
          where: {
            userId: user.id,
            webAppId: webapp.id
          }
        })

        if (!existing) {
          // Create permission
          const permission = await prisma.webAppPermission.create({
            data: {
              userId: user.id,
              webAppId: webapp.id,
              canAccess: true
            }
          })
          permissions.push(permission)
        }
      }
    }

    return NextResponse.json({ 
      message: `Created ${permissions.length} permissions`,
      permissions: permissions.length
    })

  } catch (error) {
    console.error('Error auto-creating permissions:', error)
    return NextResponse.json({ error: 'Failed to auto-create permissions' }, { status: 500 })
  }
}
