import { NextRequest, NextResponse } from 'next/server'

// Simple mock users for testing admin panel
const mockUsers = [
  {
    id: 'test-user-1',
    email: '<EMAIL>',
    name: 'Test Admin',
    role: 'SUPER_ADMIN',
    adminLevel: 100,
    isActive: true,
    organizationId: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastLoginAt: new Date().toISOString(),
    organization: null,
    webAppPermissions: []
  }
]

// GET /api/user-management - Get users (for admin panel)
export async function GET(request: NextRequest) {
  try {
    console.log('User management API - GET request')
    return NextResponse.json(mockUsers)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })
  }
}

// PUT /api/user-management - Update user
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    console.log('User management API - PUT request:', body)
    
    // Mock update response
    return NextResponse.json({ message: 'User updated successfully' })
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
  }
}
