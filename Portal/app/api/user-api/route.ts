import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function GET() {
  return NextResponse.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '2.0'
  })
}

// POST /api/user-api - Create or update user (called during login)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { email, name, tenantId } = body

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 })
    }

    console.log('Creating/updating user:', { email, name, tenantId })

    // Check if user already exists
    let user = await prisma.user.findUnique({
      where: { email }
    })

    if (user) {
      console.log('User exists, updating:', user.id)
      // Update existing user
      user = await prisma.user.update({
        where: { email },
        data: {
          name: name || user.name,
          lastLoginAt: new Date(),
          isActive: true
        }
      })
    } else {
      console.log('Creating new user')
      // Create new user
      user = await prisma.user.create({
        data: {
          email,
          name: name || email.split('@')[0],
          role: 'USER', // Default role, can be changed by admin
          adminLevel: 0,
          isActive: true,
          lastLoginAt: new Date()
        }
      })

      // Log the action
      await prisma.auditLog.create({
        data: {
          action: 'USER_CREATED',
          details: { userId: user.id, email: user.email },
          userId: user.id,
          targetId: user.id
        }
      })
    }

    console.log('User result:', user)
    return NextResponse.json(user, { status: user ? 200 : 201 })
  } catch (error) {
    console.error('Error creating/updating user:', error)
    return NextResponse.json({ error: 'Failed to create/update user' }, { status: 500 })
  }
}

// PUT /api/user-api - Update user (for admin operations)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      userId, 
      role, 
      adminLevel, 
      isActive, 
      organizationId,
      adminUserId 
    } = body

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...(role !== undefined && { role }),
        ...(adminLevel !== undefined && { adminLevel }),
        ...(isActive !== undefined && { isActive }),
        ...(organizationId !== undefined && { organizationId: organizationId === 'none' ? null : organizationId })
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_UPDATED',
        details: { userId, changes: { role, adminLevel, isActive, organizationId } },
        userId: adminUserId,
        targetId: userId
      }
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
  }
}
