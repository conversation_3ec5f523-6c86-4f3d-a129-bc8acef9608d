import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/permissions - Get user permissions or all permissions (admin)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const webAppId = searchParams.get('webAppId')
    const adminUserId = searchParams.get('adminUserId')

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    let whereClause: any = {}

    // Build where clause based on filters
    if (userId) whereClause.userId = userId
    if (webAppId) whereClause.webAppId = webAppId

    // If org admin, only show permissions for their organization's webapps
    if (adminUser.role === 'ORG_ADMIN') {
      whereClause.webApp = {
        organizationId: adminUser.organizationId
      }
    }

    const permissions = await prisma.webAppPermission.findMany({
      where: whereClause,
      include: {
        user: {
          select: { id: true, name: true, email: true, role: true }
        },
        webApp: {
          select: { id: true, name: true, title: true }
        }
      },
      orderBy: [
        { webApp: { name: 'asc' } },
        { user: { name: 'asc' } }
      ]
    })

    return NextResponse.json(permissions)
  } catch (error) {
    console.error('Error fetching permissions:', error)
    return NextResponse.json({ error: 'Failed to fetch permissions' }, { status: 500 })
  }
}

// POST /api/permissions - Grant permission to user
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, webAppId, canAccess, grantedById } = body

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: grantedById },
      include: { organization: true }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get the webapp and target user to check permissions
    const [webapp, targetUser] = await Promise.all([
      prisma.webApp.findUnique({ where: { id: webAppId } }),
      prisma.user.findUnique({ where: { id: userId } })
    ])

    if (!webapp || !targetUser) {
      return NextResponse.json({ error: 'Webapp or user not found' }, { status: 404 })
    }

    // If org admin, ensure they can only manage permissions for their org
    if (adminUser.role === 'ORG_ADMIN') {
      if (webapp.organizationId !== adminUser.organizationId || 
          targetUser.organizationId !== adminUser.organizationId) {
        return NextResponse.json({ error: 'Cannot manage permissions outside your organization' }, { status: 403 })
      }
    }

    // Check if user has sufficient admin level to modify target user
    if (adminUser.adminLevel <= targetUser.adminLevel && adminUser.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Cannot modify permissions for users of equal or higher admin level' }, { status: 403 })
    }

    const permission = await prisma.webAppPermission.upsert({
      where: {
        userId_webAppId: {
          userId,
          webAppId
        }
      },
      create: {
        userId,
        webAppId,
        canAccess: canAccess ?? true,
        grantedById
      },
      update: {
        canAccess: canAccess ?? true,
        grantedAt: new Date(),
        grantedById
      },
      include: {
        user: {
          select: { id: true, name: true, email: true }
        },
        webApp: {
          select: { id: true, name: true, title: true }
        }
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'PERMISSION_GRANTED',
        details: { 
          userId, 
          webAppId, 
          canAccess,
          webAppName: webapp.name,
          userName: targetUser.name || targetUser.email
        },
        userId: grantedById,
        targetId: userId
      }
    })

    return NextResponse.json(permission)
  } catch (error) {
    console.error('Error managing permission:', error)
    return NextResponse.json({ error: 'Failed to manage permission' }, { status: 500 })
  }
}

// DELETE /api/permissions - Revoke permission
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const webAppId = searchParams.get('webAppId')
    const adminUserId = searchParams.get('adminUserId')

    if (!userId || !webAppId || !adminUserId) {
      return NextResponse.json({ error: 'Missing required parameters' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get the webapp and target user to check permissions
    const [webapp, targetUser] = await Promise.all([
      prisma.webApp.findUnique({ where: { id: webAppId } }),
      prisma.user.findUnique({ where: { id: userId } })
    ])

    if (!webapp || !targetUser) {
      return NextResponse.json({ error: 'Webapp or user not found' }, { status: 404 })
    }

    // If org admin, ensure they can only manage permissions for their org
    if (adminUser.role === 'ORG_ADMIN') {
      if (webapp.organizationId !== adminUser.organizationId || 
          targetUser.organizationId !== adminUser.organizationId) {
        return NextResponse.json({ error: 'Cannot manage permissions outside your organization' }, { status: 403 })
      }
    }

    // Check if user has sufficient admin level to modify target user
    if (adminUser.adminLevel <= targetUser.adminLevel && adminUser.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Cannot modify permissions for users of equal or higher admin level' }, { status: 403 })
    }

    await prisma.webAppPermission.delete({
      where: {
        userId_webAppId: {
          userId,
          webAppId
        }
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'PERMISSION_REVOKED',
        details: { 
          userId, 
          webAppId,
          webAppName: webapp.name,
          userName: targetUser.name || targetUser.email
        },
        userId: adminUserId,
        targetId: userId
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error revoking permission:', error)
    return NextResponse.json({ error: 'Failed to revoke permission' }, { status: 500 })
  }
}
