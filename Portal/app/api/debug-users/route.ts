import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// Debug endpoint to check all users in database
export async function GET() {
  try {
    const allUsers = await prisma.user.findMany({
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        adminLevel: true,
        isActive: true,
        organizationId: true,
        createdAt: true
      }
    })

    const userCount = await prisma.user.count()

    return NextResponse.json({
      totalUsers: userCount,
      users: allUsers,
      debug: {
        timestamp: new Date().toISOString(),
        nodeEnv: process.env.NODE_ENV,
        databaseUrl: process.env.DATABASE_URL ? 'Set' : 'Not set'
      }
    })
  } catch (error) {
    console.error('Debug users error:', error)
    return NextResponse.json({ 
      error: 'Failed to fetch users',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
