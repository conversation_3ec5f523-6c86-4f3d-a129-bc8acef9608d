import { NextRequest, NextResponse } from 'next/server'

// This is a mock API endpoint - replace with actual Docker API integration
export async function GET() {
  try {
    // Mock application data
    const applications = [
      {
        id: '1',
        name: 'Frontend App',
        description: 'React application for customer portal',
        dockerImage: 'myapp/frontend:latest',
        status: 'running',
        port: 3000,
        url: 'https://frontend.example.com',
        repository: 'https://github.com/company/frontend',
        lastDeployed: new Date(Date.now() - 1000 * 60 * 60 * 2),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '2',
        name: 'API Service',
        description: 'Node.js REST API backend',
        dockerImage: 'myapp/api:latest',
        status: 'running',
        port: 8080,
        url: 'https://api.example.com',
        repository: 'https://github.com/company/api',
        lastDeployed: new Date(Date.now() - 1000 * 60 * 60 * 24),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        id: '3',
        name: 'Analytics Dashboard',
        description: 'Business intelligence dashboard',
        dockerImage: 'myapp/analytics:latest',
        status: 'stopped',
        port: 3001,
        url: null,
        repository: 'https://github.com/company/analytics',
        lastDeployed: new Date(Date.now() - 1000 * 60 * 60 * 24 * 3),
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ]

    return NextResponse.json({ 
      success: true, 
      data: applications 
    })
  } catch (error) {
    console.error('Error fetching applications:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch applications' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { name, description, dockerImage, port } = body
    
    if (!name || !dockerImage) {
      return NextResponse.json(
        { success: false, error: 'Name and Docker image are required' },
        { status: 400 }
      )
    }

    // Mock creating new application
    const newApplication = {
      id: Math.random().toString(36).substring(7),
      name,
      description: description || '',
      dockerImage,
      status: 'starting',
      port: port || 3000,
      url: null,
      repository: body.repository || null,
      lastDeployed: new Date(),
      createdAt: new Date(),
      updatedAt: new Date(),
    }

    // Here you would integrate with Docker API to actually deploy the container
    // docker run -d -p ${port}:${port} --name ${name} ${dockerImage}

    return NextResponse.json({ 
      success: true, 
      data: newApplication 
    })
  } catch (error) {
    console.error('Error creating application:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to create application' },
      { status: 500 }
    )
  }
}
