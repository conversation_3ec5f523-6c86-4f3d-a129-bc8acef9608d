import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/webapps - Get all webapps (filtered by user permissions)
export async function GET(request: NextRequest) {  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const showAll = searchParams.get('showAll') === 'true'

    console.log('WebApps API called with:', { userId, showAll })

    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }

    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true }
    })

    console.log('User found:', user ? { id: user.id, email: user.email, role: user.role, orgId: user.organizationId } : 'null')

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    let webapps

    if (showAll && (user.role === 'SUPER_ADMIN' || user.role === 'ORG_ADMIN')) {
      console.log('Admin user - showing all webapps')
      // Admin users can see all webapps in their scope
      webapps = await prisma.webApp.findMany({
        where: user.role === 'SUPER_ADMIN' ? {} : {
          organizationId: user.organizationId
        },
        include: {
          organization: true,
          createdBy: {
            select: { id: true, name: true, email: true }
          },
          permissions: {
            include: {
              user: {
                select: { id: true, name: true, email: true }
              }
            }
          }
        },
        orderBy: [
          { order: 'asc' },
          { createdAt: 'desc' }
        ]
      })
    } else {
      console.log('Regular user - showing permitted webapps')
      // Regular users only see webapps they have permission to access
      webapps = await prisma.webApp.findMany({
        where: {
          isActive: true,
          permissions: {
            some: {
              userId: userId,
              canAccess: true
            }
          }
        },
        include: {
          organization: true,
          createdBy: {
            select: { id: true, name: true, email: true }
          }
        },
        orderBy: [
          { order: 'asc' },
          { createdAt: 'desc' }
        ]
      })
    }

    console.log(`Found ${webapps.length} webapps`)
    
    return NextResponse.json(webapps)
  } catch (error) {
    console.error('Error fetching webapps:', error)
    return NextResponse.json({ error: 'Failed to fetch webapps' }, { status: 500 })
  }
}

// POST /api/webapps - Create new webapp (admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      name, 
      title, 
      description, 
      image, 
      url, 
      category, 
      tags, 
      requiresAuth, 
      organizationId,
      createdById 
    } = body

    // Verify user has admin permissions
    const user = await prisma.user.findUnique({
      where: { id: createdById }
    })

    if (!user || (user.role !== 'SUPER_ADMIN' && user.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // If org admin, ensure they can only create in their org
    if (user.role === 'ORG_ADMIN' && organizationId !== user.organizationId) {
      return NextResponse.json({ error: 'Cannot create webapp for different organization' }, { status: 403 })
    }

    const webapp = await prisma.webApp.create({
      data: {
        name,
        title,
        description,
        image,
        url,
        category,
        tags: tags || [],
        requiresAuth: requiresAuth ?? true,
        organizationId,
        createdById
      },
      include: {
        organization: true,
        createdBy: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'WEBAPP_CREATED',
        details: { webappId: webapp.id, name: webapp.name },
        userId: createdById,
        targetId: webapp.id
      }
    })

    return NextResponse.json(webapp, { status: 201 })
  } catch (error) {
    console.error('Error creating webapp:', error)
    return NextResponse.json({ error: 'Failed to create webapp' }, { status: 500 })
  }
}
