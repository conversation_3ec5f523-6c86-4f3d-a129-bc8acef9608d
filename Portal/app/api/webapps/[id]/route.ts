import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/webapps/[id] - Get specific webapp
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const webapp = await prisma.webApp.findUnique({
      where: { id: params.id },
      include: {
        organization: true,
        createdBy: {
          select: { id: true, name: true, email: true }
        },
        permissions: {
          include: {
            user: {
              select: { id: true, name: true, email: true }
            }
          }
        }
      }
    })

    if (!webapp) {
      return NextResponse.json({ error: 'Webapp not found' }, { status: 404 })
    }

    return NextResponse.json(webapp)
  } catch (error) {
    console.error('Error fetching webapp:', error)
    return NextResponse.json({ error: 'Failed to fetch webapp' }, { status: 500 })
  }
}

// PUT /api/webapps/[id] - Update webapp (admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { 
      title, 
      description, 
      image, 
      url, 
      category, 
      tags, 
      requiresAuth, 
      isActive, 
      order,
      userId // User making the update
    } = body

    // Verify user has admin permissions
    const user = await prisma.user.findUnique({
      where: { id: userId },
      include: { organization: true }
    })

    if (!user || (user.role !== 'SUPER_ADMIN' && user.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get the webapp to check permissions
    const existingWebapp = await prisma.webApp.findUnique({
      where: { id: params.id }
    })

    if (!existingWebapp) {
      return NextResponse.json({ error: 'Webapp not found' }, { status: 404 })
    }

    // If org admin, ensure they can only update webapps in their org
    if (user.role === 'ORG_ADMIN' && existingWebapp.organizationId !== user.organizationId) {
      return NextResponse.json({ error: 'Cannot update webapp from different organization' }, { status: 403 })
    }

    const updatedWebapp = await prisma.webApp.update({
      where: { id: params.id },
      data: {
        ...(title !== undefined && { title }),
        ...(description !== undefined && { description }),
        ...(image !== undefined && { image }),
        ...(url !== undefined && { url }),
        ...(category !== undefined && { category }),
        ...(tags !== undefined && { tags }),
        ...(requiresAuth !== undefined && { requiresAuth }),
        ...(isActive !== undefined && { isActive }),
        ...(order !== undefined && { order })
      },
      include: {
        organization: true,
        createdBy: {
          select: { id: true, name: true, email: true }
        }
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'WEBAPP_UPDATED',
        details: { webappId: params.id, changes: body },
        userId: userId,
        targetId: params.id
      }
    })

    return NextResponse.json(updatedWebapp)
  } catch (error) {
    console.error('Error updating webapp:', error)
    return NextResponse.json({ error: 'Failed to update webapp' }, { status: 500 })
  }
}

// DELETE /api/webapps/[id] - Delete webapp (admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({ error: 'User ID required' }, { status: 400 })
    }

    // Verify user has admin permissions
    const user = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!user || (user.role !== 'SUPER_ADMIN' && user.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get the webapp to check permissions
    const existingWebapp = await prisma.webApp.findUnique({
      where: { id: params.id }
    })

    if (!existingWebapp) {
      return NextResponse.json({ error: 'Webapp not found' }, { status: 404 })
    }

    // If org admin, ensure they can only delete webapps in their org
    if (user.role === 'ORG_ADMIN' && existingWebapp.organizationId !== user.organizationId) {
      return NextResponse.json({ error: 'Cannot delete webapp from different organization' }, { status: 403 })
    }

    await prisma.webApp.delete({
      where: { id: params.id }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'WEBAPP_DELETED',
        details: { webappId: params.id, name: existingWebapp.name },
        userId: userId,
        targetId: params.id
      }
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting webapp:', error)
    return NextResponse.json({ error: 'Failed to delete webapp' }, { status: 500 })
  }
}
