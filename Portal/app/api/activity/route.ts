import { NextResponse } from 'next/server'

export async function GET() {
  try {
    // Mock recent activity data - in a real app, this would come from your database/logging system
    const activities = [
      {
        id: '1',
        action: 'Deployed',
        target: 'Frontend App',
        user: 'System',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
        status: 'success',
      },
      {
        id: '2',
        action: 'Restarted',
        target: 'API Service',
        user: 'System',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        status: 'success',
      },
      {
        id: '3',
        action: 'Created',
        target: 'Analytics Dashboard',
        user: 'System',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
        status: 'success',
      },
      {
        id: '4',
        action: 'Failed deployment',
        target: 'Backend Service',
        user: 'System',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
        status: 'error',
      },
      {
        id: '5',
        action: 'Updated',
        target: 'Database Schema',
        user: 'System',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        status: 'success',
      },
    ]

    return NextResponse.json({ 
      success: true, 
      data: activities 
    })
  } catch (error) {
    console.error('Error fetching recent activity:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to fetch recent activity' },
      { status: 500 }
    )
  }
}
