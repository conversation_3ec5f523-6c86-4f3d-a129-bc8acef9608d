import { NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

export async function POST() {
  try {
    // Find the first user to be the creator
    const firstUser = await prisma.user.findFirst()
    
    if (!firstUser) {
      return NextResponse.json({ error: 'No users found' }, { status: 404 })
    }

    console.log('Found user:', firstUser.email)

    // Check if test webapp already exists
    const existing = await prisma.webApp.findFirst({
      where: { name: 'test-app' }
    })

    if (existing) {
      return NextResponse.json({ message: 'Test webapp already exists', webapp: existing })
    }

    // Create a test webapp
    const webapp = await prisma.webApp.create({
      data: {
        name: 'test-app',
        title: 'Test Application',
        description: 'A test application for the dashboard',
        url: 'https://example.com',
        category: 'Productivity',
        tags: ['test', 'example'],
        requiresAuth: false,
        isActive: true,
        order: 1,
        createdById: firstUser.id,
        organizationId: firstUser.organizationId
      }
    })

    console.log('Created webapp:', webapp)

    // Check the count
    const count = await prisma.webApp.count()
    console.log('Total webapps in database:', count)

    return NextResponse.json({ 
      message: 'Test webapp created successfully', 
      webapp,
      totalWebapps: count 
    })

  } catch (error) {
    console.error('Error creating test webapp:', error)
    return NextResponse.json({ error: 'Failed to create test webapp' }, { status: 500 })
  }
}
