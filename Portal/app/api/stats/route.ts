import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/stats - Get dashboard statistics
export async function GET(request: NextRequest) {  try {
    const { searchParams } = new URL(request.url)
    const adminUserId = searchParams.get('adminUserId')

    console.log('Stats API called with adminUserId:', adminUserId)

    if (!adminUserId) {
      console.log('No adminUserId provided, returning error')
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }    // Get stats based on user role
    let userWhereClause: any = {}
    let webAppWhereClause: any = {}
    
    // If org admin, filter by their organization
    if (adminUser.role === 'ORG_ADMIN' && adminUser.organizationId) {
      userWhereClause.organizationId = adminUser.organizationId
      webAppWhereClause.organizationId = adminUser.organizationId
    }

    console.log('Fetching stats for user:', adminUser.email, 'Role:', adminUser.role)
    console.log('User where clause:', userWhereClause)
    console.log('WebApp where clause:', webAppWhereClause)

    // Count users
    const totalUsers = await prisma.user.count({
      where: userWhereClause
    })

    // Count web apps
    const totalWebApps = await prisma.webApp.count({
      where: webAppWhereClause
    })

    // Count organizations (only for super admins)
    let totalOrganizations = 0
    if (adminUser.role === 'SUPER_ADMIN') {
      totalOrganizations = await prisma.organization.count()
    }

    // Count recent audit log actions (last 7 days)
    const recentActions = await prisma.auditLog.count({
      where: {
        createdAt: {
          gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // Last 7 days
        }
      }
    })

    console.log('Stats calculated:', { totalUsers, totalWebApps, totalOrganizations, recentActions })

    const stats = {
      totalUsers,
      totalWebApps,
      totalOrganizations,
      recentActions
    }

    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error fetching stats:', error)
    return NextResponse.json({ error: 'Failed to fetch stats' }, { status: 500 })
  }
}
