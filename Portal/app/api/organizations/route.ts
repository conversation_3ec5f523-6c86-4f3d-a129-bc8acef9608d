import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/organizations - Get organizations
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const adminUserId = searchParams.get('adminUserId')

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    let whereClause: any = {}

    // If org admin, only show their organization
    if (adminUser.role === 'ORG_ADMIN') {
      whereClause.id = adminUser.organizationId
    }

    const organizations = await prisma.organization.findMany({
      where: whereClause,
      include: {
        users: {
          select: {
            id: true,
            email: true,
            name: true,
            role: true,
            isActive: true
          }
        },
        webApps: {
          select: {
            id: true,
            name: true,
            title: true,
            isActive: true
          }
        }
      },
      orderBy: { name: 'asc' }
    })

    return NextResponse.json(organizations)
  } catch (error) {
    console.error('Error fetching organizations:', error)
    return NextResponse.json({ error: 'Failed to fetch organizations' }, { status: 500 })
  }
}

// POST /api/organizations - Create organization (super admin only)
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { name, description, logo, domain, adminUserId } = body

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify super admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || adminUser.role !== 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Only super admins can create organizations' }, { status: 403 })
    }

    const organization = await prisma.organization.create({
      data: {
        name,
        description,
        logo,
        domain
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'ORGANIZATION_CREATED',
        details: { organizationId: organization.id, name: organization.name },
        userId: adminUserId,
        targetId: organization.id
      }
    })

    return NextResponse.json(organization, { status: 201 })
  } catch (error) {
    console.error('Error creating organization:', error)
    return NextResponse.json({ error: 'Failed to create organization' }, { status: 500 })
  }
}

// PUT /api/organizations - Update organization
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { organizationId, name, description, logo, domain, isActive, adminUserId } = body

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // If org admin, ensure they can only update their organization
    if (adminUser.role === 'ORG_ADMIN' && organizationId !== adminUser.organizationId) {
      return NextResponse.json({ error: 'Cannot update different organization' }, { status: 403 })
    }

    const updatedOrganization = await prisma.organization.update({
      where: { id: organizationId },
      data: {
        ...(name !== undefined && { name }),
        ...(description !== undefined && { description }),
        ...(logo !== undefined && { logo }),
        ...(domain !== undefined && { domain }),
        ...(isActive !== undefined && { isActive })
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'ORGANIZATION_UPDATED',
        details: { organizationId, changes: { name, description, logo, domain, isActive } },
        userId: adminUserId,
        targetId: organizationId
      }
    })

    return NextResponse.json(updatedOrganization)
  } catch (error) {
    console.error('Error updating organization:', error)
    return NextResponse.json({ error: 'Failed to update organization' }, { status: 500 })
  }
}
