import { NextRequest, NextResponse } from 'next/server'
import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

// GET /api/users - Get users (admin only)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const adminUserId = searchParams.get('adminUserId')
    const organizationId = searchParams.get('organizationId')

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    console.log('Admin user lookup:', adminUser)

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      console.log('Insufficient permissions for user:', adminUser?.role)
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    let whereClause: any = {}

    // If org admin, only show users from their organization
    if (adminUser.role === 'ORG_ADMIN') {
      whereClause.organizationId = adminUser.organizationId
      console.log('ORG_ADMIN filter - organizationId:', adminUser.organizationId)
    } else if (organizationId) {
      whereClause.organizationId = organizationId
      console.log('Filtering by organizationId:', organizationId)
    } else {
      console.log('SUPER_ADMIN - no organization filter')
    }

    console.log('Where clause:', whereClause)

    const users = await prisma.user.findMany({
      where: whereClause,
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        role: true,
        adminLevel: true,
        isActive: true,
        organizationId: true,
        createdAt: true,
        updatedAt: true,
        lastLoginAt: true,
        organization: {
          select: {
            id: true,
            name: true
          }
        },
        webAppPermissions: {
          include: {
            webApp: {
              select: {
                id: true,
                name: true,
                title: true
              }
            }
          }
        }
      },      orderBy: [
        { role: 'asc' },
        { name: 'asc' }
      ]
    })

    console.log('Users found:', users.length)
    console.log('Users data:', users)

    return NextResponse.json(users)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })
  }
}

// PUT /api/users - Update user (admin only)
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { 
      userId, 
      role, 
      adminLevel, 
      isActive, 
      organizationId,
      adminUserId 
    } = body

    if (!adminUserId) {
      return NextResponse.json({ error: 'Admin user ID required' }, { status: 400 })
    }

    // Verify admin permissions
    const adminUser = await prisma.user.findUnique({
      where: { id: adminUserId }
    })

    if (!adminUser || (adminUser.role !== 'SUPER_ADMIN' && adminUser.role !== 'ORG_ADMIN')) {
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 })
    }

    // Get target user
    const targetUser = await prisma.user.findUnique({
      where: { id: userId }
    })

    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Check permissions for modifying the target user
    if (adminUser.role === 'ORG_ADMIN') {
      // Org admins can only modify users in their organization
      if (targetUser.organizationId !== adminUser.organizationId) {
        return NextResponse.json({ error: 'Cannot modify users outside your organization' }, { status: 403 })
      }
      
      // Org admins cannot modify other admins or promote users to admin
      if (targetUser.adminLevel >= adminUser.adminLevel || 
          (adminLevel && adminLevel >= adminUser.adminLevel)) {
        return NextResponse.json({ error: 'Cannot modify users of equal or higher admin level' }, { status: 403 })
      }
    }

    // Super admins can modify anyone, but we still check some constraints
    if (adminUser.role !== 'SUPER_ADMIN' && targetUser.role === 'SUPER_ADMIN') {
      return NextResponse.json({ error: 'Cannot modify super admin users' }, { status: 403 })
    }

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: {
        ...(role !== undefined && { role }),
        ...(adminLevel !== undefined && { adminLevel }),
        ...(isActive !== undefined && { isActive }),
        ...(organizationId !== undefined && { organizationId })
      },
      select: {
        id: true,
        email: true,
        name: true,
        avatar: true,
        role: true,
        adminLevel: true,
        isActive: true,
        organizationId: true,
        organization: {
          select: {
            id: true,
            name: true
          }
        }
      }
    })

    // Log the action
    await prisma.auditLog.create({
      data: {
        action: 'USER_UPDATED',
        details: { 
          targetUserId: userId,
          changes: { role, adminLevel, isActive, organizationId },
          targetUserName: targetUser.name || targetUser.email
        },
        userId: adminUserId,
        targetId: userId
      }
    })

    return NextResponse.json(updatedUser)
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
  }
}
