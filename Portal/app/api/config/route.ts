import { NextResponse } from 'next/server'

export async function GET() {
  // Try both with and without NEXT_PUBLIC_ prefix since Railway might set them differently
  const clientId = process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID || process.env.AZURE_AD_CLIENT_ID || 'b770c9ac-d8b5-44dd-a1eb-df461141421b'
  const tenantId = process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID || process.env.AZURE_AD_TENANT_ID || '24ac83b1-8151-4c00-b0c7-d53d0b42bf97'
  const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_URI || process.env.REDIRECT_URI || 'https://qr-portal-production.up.railway.app'
  const appName = process.env.NEXT_PUBLIC_APP_NAME || process.env.APP_NAME || 'QuantomRhino Portal'
  const companyName = process.env.NEXT_PUBLIC_COMPANY_NAME || process.env.COMPANY_NAME || 'QuantomRhino'

  console.log('All Railway environment variables (filtered):', Object.keys(process.env).filter(key => 
    key.includes('AZURE') || key.includes('CLIENT') || key.includes('TENANT') || key.includes('REDIRECT')
  ))

  console.log('Raw environment values:', {
    'NEXT_PUBLIC_AZURE_AD_CLIENT_ID': process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID,
    'AZURE_AD_CLIENT_ID': process.env.AZURE_AD_CLIENT_ID,
    'NEXT_PUBLIC_AZURE_AD_TENANT_ID': process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID,
    'AZURE_AD_TENANT_ID': process.env.AZURE_AD_TENANT_ID,
    'NEXT_PUBLIC_REDIRECT_URI': process.env.NEXT_PUBLIC_REDIRECT_URI,
    'REDIRECT_URI': process.env.REDIRECT_URI,
  })

  const config = {
    NEXT_PUBLIC_AZURE_AD_CLIENT_ID: clientId,
    NEXT_PUBLIC_AZURE_AD_TENANT_ID: tenantId,
    NEXT_PUBLIC_REDIRECT_URI: redirectUri,
    NEXT_PUBLIC_APP_NAME: appName,
    NEXT_PUBLIC_COMPANY_NAME: companyName,
  }

  console.log('Sending config:', config)

  return NextResponse.json(config)
}
