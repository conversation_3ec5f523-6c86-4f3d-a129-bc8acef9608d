import { NextResponse } from 'next/server'

export async function GET() {
  // Debug all environment variables
  const allEnvKeys = Object.keys(process.env)
  const azureKeys = allEnvKeys.filter(key => 
    key.includes('AZURE') || key.includes('CLIENT') || key.includes('TENANT')
  )
  
  console.log('=== FRESH CONFIG API DEBUG ===')
  console.log('All Azure-related env keys:', azureKeys)
  console.log('Raw values:')
  azureKeys.forEach(key => {
    console.log(`${key}: ${process.env[key] || 'undefined'}`)
  })
  
  // Get values with multiple fallbacks
  const clientId = process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID || process.env.AZURE_AD_CLIENT_ID || 'b770c9ac-d8b5-44dd-a1eb-df461141421b'
  const tenantId = process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID || process.env.AZURE_AD_TENANT_ID || '24ac83b1-8151-4c00-b0c7-d53d0b42bf97'
  const redirectUri = process.env.NEXT_PUBLIC_REDIRECT_URI || process.env.REDIRECT_URI || 'https://qr-portal-production.up.railway.app'
  
  const result = {
    success: true,
    timestamp: new Date().toISOString(),
    environment: {
      NEXT_PUBLIC_AZURE_AD_CLIENT_ID: clientId,
      NEXT_PUBLIC_AZURE_AD_TENANT_ID: tenantId,
      NEXT_PUBLIC_REDIRECT_URI: redirectUri,
      NEXT_PUBLIC_APP_NAME: process.env.NEXT_PUBLIC_APP_NAME || 'QuantomRhino Portal',
      NEXT_PUBLIC_COMPANY_NAME: process.env.NEXT_PUBLIC_COMPANY_NAME || 'QuantomRhino',
    },
    debug: {
      foundAzureKeys: azureKeys.length,
      clientIdSource: process.env.NEXT_PUBLIC_AZURE_AD_CLIENT_ID ? 'NEXT_PUBLIC_' : process.env.AZURE_AD_CLIENT_ID ? 'fallback' : 'none',
      tenantIdSource: process.env.NEXT_PUBLIC_AZURE_AD_TENANT_ID ? 'NEXT_PUBLIC_' : process.env.AZURE_AD_TENANT_ID ? 'fallback' : 'none',
    }
  }
  
  console.log('Sending result:', result)
  
  return NextResponse.json(result)
}
