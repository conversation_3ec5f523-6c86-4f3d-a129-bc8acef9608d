@tailwind base;
@tailwind components;
@tailwind utilities;

/* Modern Animations & Effects */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
  50% { box-shadow: 0 0 40px rgba(59, 130, 246, 0.6); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Custom Utility Classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-color: #6366f1;
  --primary-dark: #4f46e5;
  --secondary-color: #06b6d4;
  --accent-color: #f59e0b;
  --background-color: #fafbfc;
  --surface-color: #ffffff;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --border-color: #e5e7eb;
  --border-light: #f3f4f6;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Glass Morphism Effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* Smooth Transitions */
* {
  transition: all 0.3s ease;
}

/* Focus States */
button:focus,
input:focus,
textarea:focus {
  outline: none;
  ring: 2px;
  ring-color: rgba(59, 130, 246, 0.5);
}

/* Modern Card Hover Effects */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Loading Spinner Enhancement */
.spinner {
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Dark Theme Component Overrides */
@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-2.5 px-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transform hover:scale-[1.02] active:scale-[0.98];
  }

  .btn-secondary {
    @apply bg-white/10 hover:bg-white/20 text-white font-medium py-2.5 px-6 rounded-xl border border-white/20 hover:border-white/30 shadow-lg hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 backdrop-blur-lg;
  }

  .btn-ghost {
    @apply bg-transparent hover:bg-white/10 text-slate-300 hover:text-white font-medium py-2.5 px-6 rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white/30 focus:ring-offset-2;
  }

  .input-field {
    @apply w-full px-4 py-3 rounded-xl border border-white/20 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 placeholder-slate-400 text-white bg-white/10 backdrop-blur-lg hover:bg-white/15 focus:bg-white/15;
  }

  .card {
    @apply bg-white/5 rounded-2xl border border-white/10 shadow-xl hover:shadow-2xl transition-all duration-300 p-6 backdrop-blur-lg hover:bg-white/10;
  }

  .card-elevated {
    @apply bg-white/10 rounded-2xl shadow-2xl hover:shadow-3xl transition-all duration-300 p-6 border border-white/20 backdrop-blur-xl hover:bg-white/15;
  }

  .modal-backdrop {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50;
  }

  .modal-content {
    @apply bg-slate-800/95 backdrop-blur-xl rounded-2xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto border border-white/10;
  }

  .gradient-bg {
    @apply bg-gradient-to-br from-slate-900 via-slate-800/95 to-slate-900;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl;
  }
}

/* Dark Theme Headings */
h1, h2, h3, h4, h5, h6 {
  @apply text-white font-semibold leading-tight;
}

h1 { @apply text-4xl lg:text-5xl bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent; }
h2 { @apply text-3xl lg:text-4xl text-white; }
h3 { @apply text-2xl lg:text-3xl text-white; }
h4 { @apply text-xl lg:text-2xl text-white; }
h5 { @apply text-lg lg:text-xl text-white; }
h6 { @apply text-base lg:text-lg text-white; }

/* Dark Theme Links */
a {
  @apply text-blue-400 hover:text-blue-300 transition-colors duration-300;
}

/* Dark Theme Form Elements */
input, textarea, select {
  @apply input-field;
}

label {
  @apply block text-sm font-medium text-white mb-2;
}

/* Dark Theme Loading States */
.loading {
  @apply flex justify-center items-center min-h-screen;
}

.spinner {
  @apply animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500;
}

/* App Layout */
.app-container {
  @apply min-h-screen gradient-bg;
}

.main-content {
  @apply flex min-h-screen;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-10px); }
  to { opacity: 1; transform: translateX(0); }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

/* Custom grid pattern for login page */
.bg-grid-pattern {
  background-image:
    linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    @apply flex-col;
  }

  .btn-primary, .btn-secondary, .btn-ghost {
    @apply w-full;
  }
}
