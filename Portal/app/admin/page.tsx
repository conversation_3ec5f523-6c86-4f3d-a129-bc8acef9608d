'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/store/authStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { WebAppManager } from '@/components/admin/webapp-manager'
import { UserManager } from '@/components/admin/user-manager'
import { PermissionManager } from '@/components/admin/permission-manager'
import { OrganizationManager } from '@/components/admin/organization-manager'
import { AuditLogViewer } from '@/components/admin/audit-log-viewer'
import { Shield, Users, Globe, Building, ClipboardList, Settings } from 'lucide-react'

export default function AdminPage() {
  const router = useRouter()
  const { isAuthenticated, user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/')
      return
    }

    // Check if user has admin permissions
    if (!user || (user.role !== 'SUPER_ADMIN' && user.role !== 'ORG_ADMIN')) {
      router.push('/dashboard')
      return
    }
  }, [isAuthenticated, user, router])

  if (!isAuthenticated || !user || (user.role !== 'SUPER_ADMIN' && user.role !== 'ORG_ADMIN')) {
    return null
  }

  const isSuper = user.role === 'SUPER_ADMIN'

  return (    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card shadow-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground flex items-center gap-2">
                <Shield className="h-6 w-6 text-primary" />
                Admin Dashboard
              </h1>
              <p className="text-muted-foreground">
                {isSuper ? 'Super Administrator' : 'Organization Administrator'} - {user.name || user.email} (Level {user.adminLevel})
              </p>
            </div>
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard')}
            >
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="webapps" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Web Apps
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Permissions
            </TabsTrigger>
            {isSuper && (
              <TabsTrigger value="organizations" className="flex items-center gap-2">
                <Building className="h-4 w-4" />
                Organizations
              </TabsTrigger>
            )}
            <TabsTrigger value="audit" className="flex items-center gap-2">
              <ClipboardList className="h-4 w-4" />
              Audit Logs
            </TabsTrigger>
          </TabsList>          <TabsContent value="overview">
            <AdminOverview user={user} setActiveTab={setActiveTab} />
          </TabsContent>

          <TabsContent value="webapps">
            <WebAppManager user={user} />
          </TabsContent>

          <TabsContent value="users">
            <UserManager user={user} />
          </TabsContent>

          <TabsContent value="permissions">
            <PermissionManager user={user} />
          </TabsContent>

          {isSuper && (
            <TabsContent value="organizations">
              <OrganizationManager user={user} />
            </TabsContent>
          )}

          <TabsContent value="audit">
            <AuditLogViewer user={user} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

// Admin Overview Component
function AdminOverview({ user, setActiveTab }: { user: any; setActiveTab: (tab: string) => void }) {
  const handleQuickAction = (action: string) => {
    setActiveTab(action)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Welcome to Admin Dashboard</h2>
        <p className="text-muted-foreground">
          Manage users, organizations, web applications, and system permissions
        </p>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button 
            className="h-16 flex-col gap-2"
            onClick={() => handleQuickAction('webapps')}
          >
            <Globe className="h-6 w-6" />
            Manage Web Apps
          </Button>
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-2"
            onClick={() => handleQuickAction('users')}
          >
            <Users className="h-6 w-6" />
            Manage Users
          </Button>
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-2"
            onClick={() => handleQuickAction('permissions')}
          >
            <Shield className="h-6 w-6" />
            Set Permissions
          </Button>
        </CardContent>
      </Card>

      {/* Admin Info */}
      <Card>
        <CardHeader>
          <CardTitle>Admin Information</CardTitle>
          <CardDescription>Your administrative access level</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Role:</span>
              <Badge variant={user.role === 'SUPER_ADMIN' ? 'default' : 'secondary'}>
                {user.role === 'SUPER_ADMIN' ? 'Super Administrator' : 'Organization Administrator'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Admin Level:</span>
              <span className="font-medium">{user.adminLevel}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Email:</span>
              <span className="font-medium">{user.email}</span>
            </div>
            {user.organization && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Organization:</span>
                <span className="font-medium">{user.organization.name}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Available Features */}
      <Card>
        <CardHeader>
          <CardTitle>Available Features</CardTitle>
          <CardDescription>Administrative capabilities based on your role</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Web Applications
              </h4>
              <p className="text-sm text-muted-foreground">
                Create, edit, and manage web applications. Control access permissions and deployment settings.
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Users className="h-4 w-4" />
                User Management
              </h4>
              <p className="text-sm text-muted-foreground">
                Add, edit, and manage user accounts. Set roles, permissions, and organizational assignments.
              </p>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Permissions
              </h4>
              <p className="text-sm text-muted-foreground">
                Configure user permissions for web applications and system features.
              </p>
            </div>
            
            {user.role === 'SUPER_ADMIN' && (
              <div className="space-y-2">
                <h4 className="font-medium flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Organizations
                </h4>
                <p className="text-sm text-muted-foreground">
                  Create and manage organizations. Assign users and configure organizational settings.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
