'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/store/authStore'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { WebAppManager } from '@/components/admin/webapp-manager'
import { UserManager } from '@/components/admin/user-manager'
import { PermissionManager } from '@/components/admin/permission-manager'
import { OrganizationManager } from '@/components/admin/organization-manager'
import { AuditLogViewer } from '@/components/admin/audit-log-viewer'
import { Shield, Users, Globe, Building, ClipboardList, Settings } from 'lucide-react'

export default function AdminPage() {
  const router = useRouter()
  const { isAuthenticated, user } = useAuthStore()
  const [activeTab, setActiveTab] = useState('overview')

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/')
      return
    }

    // Check if user has admin permissions
    if (!user || (user.role !== 'SUPER_ADMIN' && user.role !== 'ORG_ADMIN')) {
      router.push('/dashboard')
      return
    }
  }, [isAuthenticated, user, router])

  if (!isAuthenticated || !user || (user.role !== 'SUPER_ADMIN' && user.role !== 'ORG_ADMIN')) {
    return null
  }

  const isSuper = user.role === 'SUPER_ADMIN'

  return (
    <div className="min-h-screen pt-32">
      {/* Header */}
      <div className="border-b border-white/10 bg-white/5 backdrop-blur-lg shadow-xl">
        <div className="container mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white flex items-center gap-3 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                <Shield className="h-8 w-8 text-blue-400" />
                Admin Dashboard
              </h1>
              <p className="text-slate-400 mt-2">
                {isSuper ? 'Super Administrator' : 'Organization Administrator'} - {user.name || user.email} (Level {user.adminLevel})
              </p>
            </div>
            <Button
              variant="outline"
              onClick={() => router.push('/dashboard')}
              className="bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30"
            >
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-4 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-white/10 border-white/20 backdrop-blur-lg">
            <TabsTrigger value="overview" className="flex items-center gap-2 text-slate-300 data-[state=active]:text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600">
              <Settings className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="webapps" className="flex items-center gap-2 text-slate-300 data-[state=active]:text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600">
              <Globe className="h-4 w-4" />
              Web Apps
            </TabsTrigger>
            <TabsTrigger value="users" className="flex items-center gap-2 text-slate-300 data-[state=active]:text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600">
              <Users className="h-4 w-4" />
              Users
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center gap-2 text-slate-300 data-[state=active]:text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600">
              <Shield className="h-4 w-4" />
              Permissions
            </TabsTrigger>
            {isSuper && (
              <TabsTrigger value="organizations" className="flex items-center gap-2 text-slate-300 data-[state=active]:text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600">
                <Building className="h-4 w-4" />
                Organizations
              </TabsTrigger>
            )}
            <TabsTrigger value="audit" className="flex items-center gap-2 text-slate-300 data-[state=active]:text-white data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-purple-600">
              <ClipboardList className="h-4 w-4" />
              Audit Logs
            </TabsTrigger>
          </TabsList>          <TabsContent value="overview">
            <AdminOverview user={user} setActiveTab={setActiveTab} />
          </TabsContent>

          <TabsContent value="webapps">
            <WebAppManager user={user} />
          </TabsContent>

          <TabsContent value="users">
            <UserManager user={user} />
          </TabsContent>

          <TabsContent value="permissions">
            <PermissionManager user={user} />
          </TabsContent>

          {isSuper && (
            <TabsContent value="organizations">
              <OrganizationManager user={user} />
            </TabsContent>
          )}

          <TabsContent value="audit">
            <AuditLogViewer user={user} />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

// Admin Overview Component
function AdminOverview({ user, setActiveTab }: { user: any; setActiveTab: (tab: string) => void }) {
  const handleQuickAction = (action: string) => {
    setActiveTab(action)
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-white">Welcome to Admin Dashboard</h2>
        <p className="text-slate-400">
          Manage users, organizations, web applications, and system permissions
        </p>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Common administrative tasks</CardDescription>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Button 
            className="h-16 flex-col gap-2"
            onClick={() => handleQuickAction('webapps')}
          >
            <Globe className="h-6 w-6" />
            Manage Web Apps
          </Button>
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-2"
            onClick={() => handleQuickAction('users')}
          >
            <Users className="h-6 w-6" />
            Manage Users
          </Button>
          <Button 
            variant="outline" 
            className="h-16 flex-col gap-2"
            onClick={() => handleQuickAction('permissions')}
          >
            <Shield className="h-6 w-6" />
            Set Permissions
          </Button>
        </CardContent>
      </Card>

      {/* Admin Info */}
      <Card>
        <CardHeader>
          <CardTitle>Admin Information</CardTitle>
          <CardDescription>Your administrative access level</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-slate-400">Role:</span>
              <Badge variant={user.role === 'SUPER_ADMIN' ? 'default' : 'secondary'} className="bg-gradient-to-r from-blue-600 to-purple-600 text-white border-0">
                {user.role === 'SUPER_ADMIN' ? 'Super Administrator' : 'Organization Administrator'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Admin Level:</span>
              <span className="font-medium text-white">{user.adminLevel}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-slate-400">Email:</span>
              <span className="font-medium text-white">{user.email}</span>
            </div>
            {user.organization && (
              <div className="flex justify-between">
                <span className="text-slate-400">Organization:</span>
                <span className="font-medium text-white">{user.organization.name}</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Available Features */}
      <Card>
        <CardHeader>
          <CardTitle>Available Features</CardTitle>
          <CardDescription>Administrative capabilities based on your role</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2 text-white">
                <Globe className="h-4 w-4 text-blue-400" />
                Web Applications
              </h4>
              <p className="text-sm text-slate-400">
                Create, edit, and manage web applications. Control access permissions and deployment settings.
              </p>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2 text-white">
                <Users className="h-4 w-4 text-green-400" />
                User Management
              </h4>
              <p className="text-sm text-slate-400">
                Add, edit, and manage user accounts. Set roles, permissions, and organizational assignments.
              </p>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium flex items-center gap-2 text-white">
                <Shield className="h-4 w-4 text-purple-400" />
                Permissions
              </h4>
              <p className="text-sm text-slate-400">
                Configure user permissions for web applications and system features.
              </p>
            </div>

            {user.role === 'SUPER_ADMIN' && (
              <div className="space-y-2">
                <h4 className="font-medium flex items-center gap-2 text-white">
                  <Building className="h-4 w-4 text-orange-400" />
                  Organizations
                </h4>
                <p className="text-sm text-slate-400">
                  Create and manage organizations. Assign users and configure organizational settings.
                </p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
