'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/store/authStore'
import { DashboardHeader } from '@/components/dashboard/dashboard-header'
import { ApplicationGrid } from '@/components/dashboard/application-grid'
import { RecentActivity } from '@/components/dashboard/recent-activity'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { WebAppShowcase } from '@/components/dashboard/webapp-showcase'
import { Button } from '@/components/ui/button'
import { Shield } from 'lucide-react'

export default function DashboardPage() {
  const router = useRouter()
  const { isAuthenticated, user } = useAuthStore()

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/')
    }
  }, [isAuthenticated, router])

  if (!isAuthenticated || !user) {
    return null
  }

  return (
    <div className="min-h-screen">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-8 pt-24">
        <div className="space-y-8">
          {/* Welcome Section */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-white bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
                Welcome back, {user.name}
              </h1>
              <p className="text-slate-300 mt-1">
                Access your web applications and manage your account
              </p>
              <p className="text-slate-400 text-sm mt-1">
                Automate to Elevate® your productivity
              </p>
            </div>
            <div className="flex items-center gap-3">
              {(user.role === 'SUPER_ADMIN' || user.role === 'ORG_ADMIN') && (
                <Button
                  variant="outline"
                  onClick={() => router.push('/admin')}
                  className="flex items-center gap-2 bg-white/10 border-white/20 text-white hover:bg-white/20 hover:border-white/30 transition-all duration-300"
                >
                  <Shield className="h-4 w-4" />
                  Admin Panel
                </Button>
              )}
              <QuickActions />
            </div>
          </div>          {/* Web Applications Showcase */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold text-white">Web Applications</h2>
              <p className="text-slate-400">Applications you have access to</p>
            </div>
            <WebAppShowcase />
          </div>          {/* Legacy Application Management - Hidden for now */}
          <div className="hidden">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <ApplicationGrid />
              </div>
              <div className="space-y-8">
                <RecentActivity />
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
