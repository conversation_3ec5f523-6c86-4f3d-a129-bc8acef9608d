'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/store/authStore'
import { getMsalInstance } from '@/lib/auth/msalInit'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { LoginForm } from '@/components/auth/login-form'

export default function HomePage() {
  const router = useRouter()
  const { isAuthenticated, isLoading, setLoading, user } = useAuthStore()

  console.log('HomePage render - Auth state:', { isAuthenticated, isLoading, hasUser: !!user })
  
  useEffect(() => {
    const handleRedirectPromise = async () => {
      try {
        setLoading(true)
        const msalInstance = await getMsalInstance()
        if (msalInstance) {
          const response = await msalInstance.handleRedirectPromise()
          if (response && response.account) {
            // Handle successful login from redirect
            console.log('Login successful from redirect:', response)
            
            const { setAccount, createOrUpdateUser } = useAuthStore.getState()
            setAccount(response.account)
            
            // Create or update user in database
            const dbUser = await createOrUpdateUser(response.account)
            
            if (dbUser) {
              console.log('User created/updated in database:', dbUser)
            } else {
              console.error('Failed to create/update user in database')
            }
          }
        }
      } catch (error) {
        console.error('Login error:', error)
      } finally {
        setLoading(false)
      }
    }

    handleRedirectPromise()
  }, [setLoading])
  useEffect(() => {
    console.log('Auth state changed:', { isAuthenticated, isLoading })
    if (isAuthenticated && !isLoading) {
      console.log('Redirecting to dashboard...')
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (isAuthenticated) {
    return null // Will redirect to dashboard
  }
  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="relative max-w-md w-full space-y-8 p-8">
        <div className="text-center space-y-6">
          <div className="flex justify-center">
            <img
              src="https://cdn.prod.website-files.com/6552a024805ad417ae76ef91/6552a024805ad417ae76efba_top.svg"
              alt="QuantumRhino"
              className="h-16 w-auto float-animation"
              onError={(e) => {
                // Fallback to text if image fails to load
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'block';
              }}
            />
            <div className="text-white font-black text-2xl bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent hidden">
              QuantumRhino
            </div>
          </div>
          <div>
            <h1 className="text-3xl font-bold text-white mb-2 bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent">
              Welcome to Portal
            </h1>
            <p className="text-slate-300">
              Secure access to your web applications
            </p>
            <p className="text-slate-400 text-sm mt-2">
              Automate to Elevate® your workflow
            </p>
          </div>
        </div>
        <LoginForm />
        <div className="text-center">
          <p className="text-xs text-slate-400">
            Powered by QuantumRhino Technology
          </p>
        </div>
      </div>
    </div>
  )
}
