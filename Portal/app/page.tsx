'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/lib/store/authStore'
import { getMsalInstance } from '@/lib/auth/msalInit'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { LoginForm } from '@/components/auth/login-form'

export default function HomePage() {
  const router = useRouter()
  const { isAuthenticated, isLoading, setLoading, user } = useAuthStore()

  console.log('HomePage render - Auth state:', { isAuthenticated, isLoading, hasUser: !!user })
  
  useEffect(() => {
    const handleRedirectPromise = async () => {
      try {
        setLoading(true)
        const msalInstance = await getMsalInstance()
        if (msalInstance) {
          const response = await msalInstance.handleRedirectPromise()
          if (response && response.account) {
            // Handle successful login from redirect
            console.log('Login successful from redirect:', response)
            
            const { setAccount, createOrUpdateUser } = useAuthStore.getState()
            setAccount(response.account)
            
            // Create or update user in database
            const dbUser = await createOrUpdateUser(response.account)
            
            if (dbUser) {
              console.log('User created/updated in database:', dbUser)
            } else {
              console.error('Failed to create/update user in database')
            }
          }
        }
      } catch (error) {
        console.error('Login error:', error)
      } finally {
        setLoading(false)
      }
    }

    handleRedirectPromise()
  }, [setLoading])
  useEffect(() => {
    console.log('Auth state changed:', { isAuthenticated, isLoading })
    if (isAuthenticated && !isLoading) {
      console.log('Redirecting to dashboard...')
      router.push('/dashboard')
    }
  }, [isAuthenticated, isLoading, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (isAuthenticated) {
    return null // Will redirect to dashboard
  }
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-700">
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="relative max-w-md w-full space-y-8 p-8">        <div className="text-center space-y-6">
          <div className="flex justify-center">
            <img 
              src="/quantumrhino.svg" 
              alt="QuantumRhino" 
              className="h-16 w-auto float-animation"
            />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
              Welcome to Portal
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Secure access to your containerized applications
            </p>
          </div>
        </div>
        <LoginForm />
        <div className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Powered by QuantumRhino Technology
          </p>
        </div>
      </div>
    </div>
  )
}
