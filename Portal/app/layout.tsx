import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/providers/theme-provider'
import { AuthProvider } from '@/components/providers/auth-provider'
import { QueryProvider } from '@/components/providers/query-provider'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'QuantumRhino Portal - Automate to Elevate®',
  description: 'QuantumRhino\'s secure web application portal. Automate to Elevate® your access to containerized applications with intelligent management solutions.',
  keywords: ['QuantumRhino', 'Portal', 'Web Applications', 'Container Management', 'Automation', 'Professional', 'Automate to Elevate'],
  authors: [{ name: 'QuantumRhino' }],
  robots: 'noindex, nofollow', // Private portal
  viewport: 'width=device-width, initial-scale=1',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" className="scroll-smooth" suppressHydrationWarning>
      <body className="antialiased font-sans bg-gradient-to-br from-slate-900 via-slate-800/95 to-slate-900 min-h-screen">
        {/* Subtle blue-pink overlay */}
        <div className="fixed inset-0 bg-gradient-to-br from-blue-900/10 via-transparent to-pink-900/10 pointer-events-none"></div>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem={false}
          disableTransitionOnChange
        >
          <QueryProvider>
            <AuthProvider>
              <div className="relative">
                {children}
              </div>
              <Toaster />
            </AuthProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
