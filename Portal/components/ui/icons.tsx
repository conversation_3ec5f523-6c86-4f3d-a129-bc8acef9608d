import { Loader2, Github, Settings, User, LogOut, Home, Container, Activity, Shield, ChevronDown, Search, Plus, Eye, EyeOff, RefreshCw, Trash, Edit, ExternalLink, Play, Pause, AlertCircle, Lock, CheckCircle } from "lucide-react"

export const Icons = {
  spinner: Loader2,
  microsoft: ({ className, ...props }: React.SVGProps<SVGSVGElement>) => (
    <svg
      className={className}
      {...props}
      viewBox="0 0 24 24"
      fill="currentColor"
    >
      <path d="M11.4 24H0V12.6h11.4V24zM24 24H12.6V12.6H24V24zM11.4 11.4H0V0h11.4v11.4zM24 11.4H12.6V0H24v11.4z" />
    </svg>
  ),
  github: Github,
  settings: Settings,
  user: User,
  logout: LogOut,
  home: Home,
  container: Container,
  activity: Activity,
  shield: Shield,
  chevronDown: ChevronDown,
  search: Search,
  plus: Plus,
  eye: Eye,
  eyeOff: EyeOff,
  refresh: RefreshCw,
  trash: Trash,
  edit: Edit,
  externalLink: ExternalLink,  play: Play,
  pause: Pause,
  alertCircle: AlertCircle,
  lock: Lock,
  checkCircle: CheckCircle,
}
