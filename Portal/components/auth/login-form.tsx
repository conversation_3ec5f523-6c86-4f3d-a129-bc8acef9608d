'use client'

import { useState } from 'react'
import { useMsal } from '@azure/msal-react'
import { loginRequest } from '@/lib/auth/msalInit'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Icons } from '@/components/ui/icons'
import { useAuthStore } from '@/lib/store/authStore'

export function LoginForm() {
  const { instance } = useMsal()
  const [isLoading, setIsLoading] = useState(false)
  const { setUser, setAccount } = useAuthStore()
  
  const handleLogin = async () => {
    setIsLoading(true)
    try {
      console.log('Starting login process...')
      // Use loginRedirect instead of loginPopup for better UX
      await instance.loginRedirect(loginRequest)
    } catch (error) {
      console.error('Login failed:', error)
      setIsLoading(false)
    }
    // Note: setLoading(false) is handled in the redirect flow
  }
  return (
    <Card className="w-full max-w-md mx-auto backdrop-blur-sm bg-white/90 dark:bg-gray-800/90 border-0 shadow-xl">
      <CardHeader className="space-y-2 pb-6">
        <CardTitle className="text-2xl text-center font-semibold">Sign in</CardTitle>
        <CardDescription className="text-center text-gray-600 dark:text-gray-400">
          Use your Microsoft account to access the portal
        </CardDescription>
      </CardHeader>      <CardContent className="space-y-6">
        <Button
          onClick={handleLogin}
          disabled={isLoading}
          className="w-full h-12 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transition-all duration-200 transform hover:scale-[1.02] active:scale-[0.98]"
          size="lg"
        >
          {isLoading ? (
            <>
              <Icons.spinner className="mr-2 h-5 w-5 animate-spin" />
              Redirecting to Microsoft...
            </>
          ) : (
            <>
              <Icons.microsoft className="mr-2 h-5 w-5" />
              Sign in with Microsoft
            </>
          )}
        </Button>
        
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-200 dark:border-gray-600" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white dark:bg-gray-800 px-2 text-gray-500 dark:text-gray-400">
              Secure Login
            </span>
          </div>
        </div>
        
        <div className="flex items-center justify-center space-x-4 text-xs text-gray-500 dark:text-gray-400">
          <div className="flex items-center space-x-1">
            <Icons.shield className="h-3 w-3" />
            <span>Encrypted</span>
          </div>
          <div className="flex items-center space-x-1">
            <Icons.lock className="h-3 w-3" />
            <span>Secure</span>
          </div>
          <div className="flex items-center space-x-1">
            <Icons.checkCircle className="h-3 w-3" />
            <span>Verified</span>
          </div>
        </div>
        
        <div className="text-xs text-center text-gray-500 dark:text-gray-400 leading-relaxed">
          By signing in, you agree to our{' '}
          <a href="#" className="text-blue-600 hover:text-blue-700 underline">
            terms of service
          </a>{' '}
          and{' '}
          <a href="#" className="text-blue-600 hover:text-blue-700 underline">
            privacy policy
          </a>
          .
        </div>
      </CardContent>
    </Card>
  )
}
