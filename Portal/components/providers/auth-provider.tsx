'use client'

import { useEffect, useState } from 'react'
import { MsalProvider } from '@azure/msal-react'
import { PublicClientApplication } from '@azure/msal-browser'
import { initializeMsal } from '@/lib/auth/msalInit'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [msalInstance, setMsalInstance] = useState<PublicClientApplication | null>(null)
  const [error, setError] = useState<string | null>(null)
  useEffect(() => {
    const initialize = async () => {
      try {
        const instance = await initializeMsal()
        if (instance) {
          setMsalInstance(instance)
        }
      } catch (err) {
        console.error('Failed to initialize MSAL:', err)
        setError(err instanceof Error ? err.message : 'Failed to initialize authentication')
      }
    }

    initialize()
  }, [])

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <p className="text-sm text-gray-500">
            Please check the application configuration and try again.
          </p>
        </div>
      </div>
    )
  }

  if (!msalInstance) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  return (
    <MsalProvider instance={msalInstance}>
      {children}
    </MsalProvider>
  )
}
