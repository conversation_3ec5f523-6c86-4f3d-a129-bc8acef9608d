'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { Switch } from '@/components/ui/switch'
import { Shield, Users, Globe, UserPlus, X, Check } from 'lucide-react'

interface Permission {
  id: string
  userId: string
  webAppId: string
  canAccess: boolean
  grantedAt: string
  user: {
    id: string
    name?: string
    email: string
    role: string
  }
  webApp: {
    id: string
    name: string
    title: string
  }
}

interface User {
  id: string
  name?: string
  email: string
  role: string
}

interface WebApp {
  id: string
  name: string
  title: string
  description?: string
}

interface PermissionManagerProps {
  user: any
}

export function PermissionManager({ user }: PermissionManagerProps) {
  const [permissions, setPermissions] = useState<Permission[]>([])
  const [users, setUsers] = useState<User[]>([])
  const [webapps, setWebapps] = useState<WebApp[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedUser, setSelectedUser] = useState<string>('')
  const [selectedWebApp, setSelectedWebApp] = useState<string>('')
  const [granting, setGranting] = useState(false)

  useEffect(() => {
    fetchData()
  }, [user?.id])
  const fetchData = async () => {
    if (!user?.id) return
    
    try {
      setLoading(true)
      const [permissionsRes, usersRes, webappsRes] = await Promise.all([
        fetch(`/api/permissions?adminUserId=${user.id}`),
        fetch(`/api/users?adminUserId=${user.id}`),
        fetch(`/api/webapps?userId=${user.id}&showAll=true`)
      ])

      if (!permissionsRes.ok || !usersRes.ok || !webappsRes.ok) {
        const permError = !permissionsRes.ok ? await permissionsRes.text() : null
        const userError = !usersRes.ok ? await usersRes.text() : null
        const webappError = !webappsRes.ok ? await webappsRes.text() : null
        
        console.error('API Errors:', { permError, userError, webappError })
        throw new Error(`Failed to fetch data: ${permError || userError || webappError || 'Unknown error'}`)
      }

      const [permissionsData, usersData, webappsData] = await Promise.all([
        permissionsRes.json(),
        usersRes.json(),
        webappsRes.json()
      ])

      console.log('Fetched data:', { 
        permissions: permissionsData.length || 0, 
        users: usersData.length || 0, 
        webapps: webappsData.length || 0 
      })

      setPermissions(permissionsData)
      setUsers(usersData)
      setWebapps(webappsData)
    } catch (err) {
      console.error('Error in fetchData:', err)
      setError(err instanceof Error ? err.message : 'Failed to load data')
    } finally {
      setLoading(false)
    }
  }

  const grantPermission = async () => {
    if (!selectedUser || !selectedWebApp) return

    try {
      setGranting(true)
      const response = await fetch('/api/permissions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId: selectedUser,
          webAppId: selectedWebApp,
          canAccess: true,
          grantedById: user.id
        })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to grant permission')
      }

      // Refresh data
      await fetchData()
      setSelectedUser('')
      setSelectedWebApp('')
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to grant permission')
    } finally {
      setGranting(false)
    }
  }

  const togglePermission = async (permission: Permission) => {
    try {
      if (permission.canAccess) {
        // Revoke permission
        const response = await fetch(`/api/permissions?userId=${permission.userId}&webAppId=${permission.webAppId}&adminUserId=${user.id}`, {
          method: 'DELETE'
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to revoke permission')
        }
      } else {
        // Grant permission
        const response = await fetch('/api/permissions', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId: permission.userId,
            webAppId: permission.webAppId,
            canAccess: true,
            grantedById: user.id
          })
        })

        if (!response.ok) {
          const error = await response.json()
          throw new Error(error.error || 'Failed to grant permission')
        }
      }

      // Refresh data
      await fetchData()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update permission')
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Permission Management</h2>
          <p className="text-muted-foreground">Control user access to web applications</p>
        </div>
        <Card>
          <CardContent className="p-8 text-center">
            <LoadingSpinner />
            <p className="mt-4 text-muted-foreground">Loading permissions...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h2 className="text-2xl font-bold">Permission Management</h2>
          <p className="text-muted-foreground">Control user access to web applications</p>
        </div>
        <Card>
          <CardContent className="p-8 text-center text-red-600">
            Error: {error}
            <Button 
              variant="outline" 
              onClick={fetchData} 
              className="mt-4"
            >
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Group permissions by webapp for better organization
  const permissionsByWebApp = permissions.reduce((acc, permission) => {
    const webAppId = permission.webAppId
    if (!acc[webAppId]) {
      acc[webAppId] = []
    }
    acc[webAppId].push(permission)
    return acc
  }, {} as Record<string, Permission[]>)

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Permission Management</h2>
        <p className="text-muted-foreground">Control user access to web applications</p>
      </div>

      {/* Grant New Permission */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Grant New Permission
          </CardTitle>
          <CardDescription>Give a user access to a web application</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Select User</label>
              <Select value={selectedUser} onValueChange={setSelectedUser}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a user" />
                </SelectTrigger>
                <SelectContent>
                  {users.map((u) => (
                    <SelectItem key={u.id} value={u.id}>
                      <div className="flex items-center gap-2">
                        <span>{u.name || u.email}</span>
                        <Badge variant="secondary" className="text-xs">
                          {u.role}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Select Web App</label>
              <Select value={selectedWebApp} onValueChange={setSelectedWebApp}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a web app" />
                </SelectTrigger>
                <SelectContent>
                  {webapps.map((webapp) => (
                    <SelectItem key={webapp.id} value={webapp.id}>
                      {webapp.title || webapp.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <Button 
            onClick={grantPermission}
            disabled={!selectedUser || !selectedWebApp || granting}
            className="w-full"
          >            {granting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary-foreground mr-2"></div>
                Granting Permission...
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                Grant Permission
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Current Permissions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Current Permissions
          </CardTitle>
          <CardDescription>
            Manage existing user permissions ({permissions.length} total)
          </CardDescription>
        </CardHeader>
        <CardContent>
          {permissions.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Shield className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No permissions granted yet</p>
              <p className="text-sm">Use the form above to grant permissions to users</p>
            </div>
          ) : (
            <div className="space-y-6">
              {webapps.map((webapp) => {
                const webappPermissions = permissionsByWebApp[webapp.id] || []
                if (webappPermissions.length === 0) return null

                return (
                  <div key={webapp.id} className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <h4 className="font-medium">{webapp.title || webapp.name}</h4>
                      <Badge variant="outline" className="text-xs">
                        {webappPermissions.length} user{webappPermissions.length !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                    
                    <div className="grid gap-2 ml-6">
                      {webappPermissions.map((permission) => (
                        <div 
                          key={`${permission.userId}-${permission.webAppId}`}
                          className="flex items-center justify-between p-3 border rounded-lg"
                        >
                          <div className="flex items-center gap-3">
                            <div>
                              <div className="font-medium">
                                {permission.user.name || permission.user.email}
                              </div>
                              <div className="text-sm text-muted-foreground">
                                {permission.user.email} • {permission.user.role}
                              </div>
                            </div>
                          </div>
                          
                          <div className="flex items-center gap-3">
                            <Badge 
                              variant={permission.canAccess ? "default" : "destructive"}
                              className="text-xs"
                            >
                              {permission.canAccess ? "Granted" : "Revoked"}
                            </Badge>
                            <Switch
                              checked={permission.canAccess}
                              onCheckedChange={() => togglePermission(permission)}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              <div>
                <div className="text-2xl font-bold">{users.length}</div>
                <div className="text-sm text-muted-foreground">Total Users</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Globe className="h-4 w-4 text-green-600" />
              <div>
                <div className="text-2xl font-bold">{webapps.length}</div>
                <div className="text-sm text-muted-foreground">Web Apps</div>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-purple-600" />
              <div>
                <div className="text-2xl font-bold">{permissions.filter(p => p.canAccess).length}</div>
                <div className="text-sm text-muted-foreground">Active Permissions</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
