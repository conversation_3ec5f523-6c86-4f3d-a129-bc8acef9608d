'use client'

import { useState, useEffect } from 'react'
import { useAuthStore } from '@/lib/store/authStore'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Building2, Plus, Edit, Users, Globe } from 'lucide-react'

interface Organization {
  id: string
  name: string
  description?: string
  logo?: string
  domain?: string
  isActive: boolean
  createdAt: string
  updatedAt: string
  users: Array<{
    id: string
    email: string
    name: string
    role: string
    isActive: boolean
  }>
  webApps: Array<{
    id: string
    name: string
    title: string
    isActive: boolean
  }>
}

interface OrganizationManagerProps {
  user: any
}

export function OrganizationManager({ user }: OrganizationManagerProps) {
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingOrg, setEditingOrg] = useState<Organization | null>(null)

  const fetchOrganizations = async () => {
    if (!user || user.role !== 'SUPER_ADMIN') {
      setLoading(false)
      return
    }

    try {
      const params = new URLSearchParams({
        adminUserId: user.id
      })
      
      const response = await fetch(`/api/organizations?${params}`)
      if (!response.ok) throw new Error('Failed to fetch organizations')
      
      const data = await response.json()
      setOrganizations(data)
    } catch (error) {
      console.error('Error fetching organizations:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOrganizations()
  }, [user?.id, user?.role]) // eslint-disable-line react-hooks/exhaustive-deps

  // Only show for Super Admins
  if (!user || user.role !== 'SUPER_ADMIN') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Management
          </CardTitle>
          <CardDescription>
            Access denied. Only Super Administrators can manage organizations.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const handleCreateOrganization = async (formData: FormData) => {
    try {
      const orgData = {
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        domain: formData.get('domain') as string,
        logo: formData.get('logo') as string,
        adminUserId: user.id
      }

      const response = await fetch('/api/organizations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(orgData)
      })

      if (!response.ok) throw new Error('Failed to create organization')

      setIsCreateDialogOpen(false)
      fetchOrganizations()
    } catch (error) {
      console.error('Error creating organization:', error)
    }
  }

  const handleUpdateOrganization = async (formData: FormData) => {
    if (!editingOrg) return

    try {
      const updateData = {
        organizationId: editingOrg.id,
        name: formData.get('name') as string,
        description: formData.get('description') as string,
        domain: formData.get('domain') as string,
        logo: formData.get('logo') as string,
        isActive: formData.get('isActive') === 'on',
        adminUserId: user.id
      }

      const response = await fetch('/api/organizations', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) throw new Error('Failed to update organization')

      setIsEditDialogOpen(false)
      setEditingOrg(null)
      fetchOrganizations()
    } catch (error) {
      console.error('Error updating organization:', error)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Organization Management
          </CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Organization Management
              </CardTitle>
              <CardDescription>
                Create and manage organizations. Only Super Administrators can create new organizations.
              </CardDescription>
            </div>
            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Organization
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Organization</DialogTitle>
                  <DialogDescription>
                    Create a new organization to group users and applications.
                  </DialogDescription>
                </DialogHeader>
                <form action={handleCreateOrganization} className="space-y-4">
                  <div>
                    <Label htmlFor="name">Organization Name *</Label>
                    <Input id="name" name="name" required />
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea id="description" name="description" />
                  </div>
                  <div>
                    <Label htmlFor="domain">Domain</Label>
                    <Input id="domain" name="domain" placeholder="example.com" />
                  </div>
                  <div>
                    <Label htmlFor="logo">Logo URL</Label>
                    <Input id="logo" name="logo" type="url" placeholder="https://example.com/logo.png" />
                  </div>
                  <Button type="submit" className="w-full">Create Organization</Button>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </CardHeader>
        <CardContent>
          {organizations.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <p className="text-gray-500">No organizations found. Create your first organization to get started.</p>
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {organizations.map((org) => (
                <Card key={org.id} className="relative">
                  <CardHeader className="pb-2">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-2">
                        {org.logo ? (
                          <img src={org.logo} alt={org.name} className="h-8 w-8 rounded" />
                        ) : (
                          <Building2 className="h-8 w-8 text-gray-400" />
                        )}
                        <div>
                          <CardTitle className="text-lg">{org.name}</CardTitle>
                          {org.domain && (
                            <div className="flex items-center gap-1 text-sm text-gray-500">
                              <Globe className="h-3 w-3" />
                              {org.domain}
                            </div>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingOrg(org)
                          setIsEditDialogOpen(true)
                        }}
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {org.description && (
                      <p className="text-sm text-gray-600 mb-3">{org.description}</p>
                    )}
                    <div className="flex items-center justify-between text-sm">
                      <div className="flex items-center gap-1">
                        <Users className="h-3 w-3" />
                        {org.users.length} users
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          org.isActive 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {org.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Organization Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Organization</DialogTitle>
            <DialogDescription>
              Update organization details and settings.
            </DialogDescription>
          </DialogHeader>
          {editingOrg && (
            <form action={handleUpdateOrganization} className="space-y-4">
              <div>
                <Label htmlFor="edit-name">Organization Name *</Label>
                <Input 
                  id="edit-name" 
                  name="name" 
                  defaultValue={editingOrg.name}
                  required 
                />
              </div>
              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea 
                  id="edit-description" 
                  name="description"
                  defaultValue={editingOrg.description || ''}
                />
              </div>
              <div>
                <Label htmlFor="edit-domain">Domain</Label>
                <Input 
                  id="edit-domain" 
                  name="domain"
                  defaultValue={editingOrg.domain || ''}
                  placeholder="example.com" 
                />
              </div>
              <div>
                <Label htmlFor="edit-logo">Logo URL</Label>
                <Input 
                  id="edit-logo" 
                  name="logo"
                  defaultValue={editingOrg.logo || ''}
                  type="url" 
                  placeholder="https://example.com/logo.png" 
                />
              </div>
              <div className="flex items-center space-x-2">
                <Switch 
                  id="edit-isActive" 
                  name="isActive"
                  defaultChecked={editingOrg.isActive}
                />
                <Label htmlFor="edit-isActive">Organization Active</Label>
              </div>
              <Button type="submit" className="w-full">Update Organization</Button>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
