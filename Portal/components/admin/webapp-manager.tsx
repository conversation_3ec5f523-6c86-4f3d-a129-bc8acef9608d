'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Plus, Edit, Trash2, Globe, Upload } from 'lucide-react'

interface WebApp {
  id: string
  name: string
  title: string
  description?: string
  image?: string
  url: string
  category?: string
  tags: string[]
  isActive: boolean
  requiresAuth: boolean
  order: number
  organizationId?: string
  organization?: {
    id: string
    name: string
  }
  createdBy: {
    id: string
    name?: string
    email: string
  }
}

interface Organization {
  id: string
  name: string
}

interface WebAppManagerProps {
  user: any
}

export function WebAppManager({ user }: WebAppManagerProps) {
  const [webapps, setWebapps] = useState<WebApp[]>([])
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [editingWebapp, setEditingWebapp] = useState<WebApp | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  useEffect(() => {
    fetchWebapps()
    if (user.role === 'SUPER_ADMIN') {
      fetchOrganizations()
    }
  }, [user.id, user.role]) // eslint-disable-line react-hooks/exhaustive-deps

  const fetchWebapps = async () => {
    try {
      const params = new URLSearchParams({
        userId: user.id,
        showAll: 'true'
      })
      
      const response = await fetch(`/api/webapps?${params}`)
      if (!response.ok) throw new Error('Failed to fetch webapps')
      
      const data = await response.json()
      setWebapps(data)
    } catch (error) {
      console.error('Error fetching webapps:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchOrganizations = async () => {
    try {
      const params = new URLSearchParams({
        adminUserId: user.id
      })
      
      const response = await fetch(`/api/organizations?${params}`)
      if (!response.ok) throw new Error('Failed to fetch organizations')
      
      const data = await response.json()
      setOrganizations(data)
    } catch (error) {
      console.error('Error fetching organizations:', error)
    }
  }

  const handleCreateWebapp = async (formData: FormData) => {
    try {
      const webappData = {
        name: formData.get('name') as string,
        title: formData.get('title') as string,
        description: formData.get('description') as string,
        image: formData.get('image') as string,
        url: formData.get('url') as string,
        category: formData.get('category') as string,
        tags: (formData.get('tags') as string).split(',').map(t => t.trim()).filter(Boolean),
        requiresAuth: formData.get('requiresAuth') === 'on',
        organizationId: formData.get('organizationId') as string || user.organizationId,
        createdById: user.id
      }

      const response = await fetch('/api/webapps', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(webappData)
      })

      if (!response.ok) throw new Error('Failed to create webapp')

      setIsCreateDialogOpen(false)
      fetchWebapps()
    } catch (error) {
      console.error('Error creating webapp:', error)
    }
  }

  const handleUpdateWebapp = async (formData: FormData) => {
    if (!editingWebapp) return

    try {
      const updateData = {
        title: formData.get('title') as string,
        description: formData.get('description') as string,
        image: formData.get('image') as string,
        url: formData.get('url') as string,
        category: formData.get('category') as string,
        tags: (formData.get('tags') as string).split(',').map(t => t.trim()).filter(Boolean),
        requiresAuth: formData.get('requiresAuth') === 'on',
        isActive: formData.get('isActive') === 'on',
        order: parseInt(formData.get('order') as string) || 0,
        userId: user.id
      }

      const response = await fetch(`/api/webapps/${editingWebapp.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) throw new Error('Failed to update webapp')

      setIsEditDialogOpen(false)
      setEditingWebapp(null)
      fetchWebapps()
    } catch (error) {
      console.error('Error updating webapp:', error)
    }
  }

  const handleDeleteWebapp = async (webapp: WebApp) => {
    if (!confirm(`Are you sure you want to delete "${webapp.title}"?`)) return

    try {
      const response = await fetch(`/api/webapps/${webapp.id}?userId=${user.id}`, {
        method: 'DELETE'
      })

      if (!response.ok) throw new Error('Failed to delete webapp')

      fetchWebapps()
    } catch (error) {
      console.error('Error deleting webapp:', error)
    }
  }

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading webapps...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Web Application Management</h2>
          <p className="text-muted-foreground">Create, edit, and manage web applications</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Web App
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Web Application</DialogTitle>
              <DialogDescription>
                Add a new web application to the portal
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={(e) => {
              e.preventDefault()
              handleCreateWebapp(new FormData(e.currentTarget))
            }}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Internal Name</Label>
                    <Input id="name" name="name" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="title">Display Title</Label>
                    <Input id="title" name="title" required />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea id="description" name="description" />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="url">Application URL</Label>
                  <Input id="url" name="url" type="url" required />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="image">Image URL</Label>
                    <Input id="image" name="image" type="url" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Category</Label>
                    <Input id="category" name="category" />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="tags">Tags (comma-separated)</Label>
                  <Input id="tags" name="tags" placeholder="web, dashboard, analytics" />
                </div>
                
                {user.role === 'SUPER_ADMIN' && (
                  <div className="space-y-2">
                    <Label htmlFor="organizationId">Organization</Label>
                    <Select name="organizationId">
                      <SelectTrigger>
                        <SelectValue placeholder="Select organization" />
                      </SelectTrigger>
                      <SelectContent>
                        {organizations.map(org => (
                          <SelectItem key={org.id} value={org.id}>
                            {org.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                <div className="flex items-center space-x-2">
                  <Switch id="requiresAuth" name="requiresAuth" defaultChecked />
                  <Label htmlFor="requiresAuth">Requires Authentication</Label>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Create Web App</Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Webapps Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {webapps.map((webapp) => (
          <Card key={webapp.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Globe className="h-5 w-5" />
                    {webapp.title}
                  </CardTitle>
                  <CardDescription>{webapp.description}</CardDescription>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={webapp.isActive ? "default" : "secondary"}>
                    {webapp.isActive ? "Active" : "Inactive"}
                  </Badge>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setEditingWebapp(webapp)
                      setIsEditDialogOpen(true)
                    }}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleDeleteWebapp(webapp)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium">URL</Label>
                  <p className="text-sm text-muted-foreground truncate">{webapp.url}</p>
                </div>
                
                {webapp.category && (
                  <div>
                    <Label className="text-sm font-medium">Category</Label>
                    <p className="text-sm text-muted-foreground">{webapp.category}</p>
                  </div>
                )}
                
                {webapp.tags.length > 0 && (
                  <div>
                    <Label className="text-sm font-medium">Tags</Label>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {webapp.tags.map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                <div className="flex items-center justify-between text-sm text-slate-400">
                  <span>Auth Required: {webapp.requiresAuth ? 'Yes' : 'No'}</span>
                  <span>Order: {webapp.order}</span>
                </div>

                {webapp.organization && (
                  <div>
                    <Label className="text-sm font-medium">Organization</Label>
                    <p className="text-sm text-slate-400">{webapp.organization.name}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Web Application</DialogTitle>
            <DialogDescription>
              Update the web application details
            </DialogDescription>
          </DialogHeader>
          {editingWebapp && (
            <form onSubmit={(e) => {
              e.preventDefault()
              handleUpdateWebapp(new FormData(e.currentTarget))
            }}>
              <div className="grid gap-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-title">Display Title</Label>
                  <Input 
                    id="edit-title" 
                    name="title" 
                    defaultValue={editingWebapp.title}
                    required 
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-description">Description</Label>
                  <Textarea 
                    id="edit-description" 
                    name="description" 
                    defaultValue={editingWebapp.description}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-url">Application URL</Label>
                  <Input 
                    id="edit-url" 
                    name="url" 
                    type="url" 
                    defaultValue={editingWebapp.url}
                    required 
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-image">Image URL</Label>
                    <Input 
                      id="edit-image" 
                      name="image" 
                      type="url" 
                      defaultValue={editingWebapp.image}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="edit-category">Category</Label>
                    <Input 
                      id="edit-category" 
                      name="category" 
                      defaultValue={editingWebapp.category}
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-tags">Tags (comma-separated)</Label>
                  <Input 
                    id="edit-tags" 
                    name="tags" 
                    defaultValue={editingWebapp.tags.join(', ')}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="edit-order">Display Order</Label>
                  <Input 
                    id="edit-order" 
                    name="order" 
                    type="number"
                    defaultValue={editingWebapp.order}
                  />
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="edit-requiresAuth" 
                      name="requiresAuth" 
                      defaultChecked={editingWebapp.requiresAuth}
                    />
                    <Label htmlFor="edit-requiresAuth">Requires Authentication</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Switch 
                      id="edit-isActive" 
                      name="isActive" 
                      defaultChecked={editingWebapp.isActive}
                    />
                    <Label htmlFor="edit-isActive">Active</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Update Web App</Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
