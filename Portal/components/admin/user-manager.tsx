'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Switch } from '@/components/ui/switch'
import { Users, Edit, Shield, Building, UserPlus, Search, Filter } from 'lucide-react'
import { useAuthStore } from '@/lib/store/authStore'

interface User {
  id: string
  email: string
  name?: string
  avatar?: string
  role: 'SUPER_ADMIN' | 'ORG_ADMIN' | 'USER' | 'VIEWER'
  adminLevel: number
  isActive: boolean
  organizationId?: string
  createdAt: string
  updatedAt: string
  lastLoginAt?: string
  organization?: {
    id: string
    name: string
  }
  webAppPermissions: Array<{
    id: string
    canAccess: boolean
    webApp: {
      id: string
      name: string
      title: string
    }
  }>
}

interface Organization {
  id: string
  name: string
}

interface UserManagerProps {
  user: any
}

export function UserManager({ user }: UserManagerProps) {
  const { refreshUser } = useAuthStore()
  const [users, setUsers] = useState<User[]>([])
  const [organizations, setOrganizations] = useState<Organization[]>([])
  const [loading, setLoading] = useState(true)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [roleFilter, setRoleFilter] = useState<string>('all')

  useEffect(() => {
    fetchUsers()
    if (user.role === 'SUPER_ADMIN') {
      fetchOrganizations()
    }
  }, [user.id, user.role]) // eslint-disable-line react-hooks/exhaustive-deps
  const fetchUsers = async () => {
    try {
      const params = new URLSearchParams({
        adminUserId: user.id
      })
      
      console.log('Fetching users with params:', params.toString())
      console.log('Admin user:', user)
      
      const response = await fetch(`/api/users?${params}`)
      if (!response.ok) {
        const errorText = await response.text()
        console.error('API Error:', response.status, errorText)
        throw new Error('Failed to fetch users')
      }
      
      const data = await response.json()
      console.log('Fetched users data:', data)
      
      // Ensure data is always an array
      setUsers(Array.isArray(data) ? data : [])
    } catch (error) {
      console.error('Error fetching users:', error)
      // Set empty array on error to prevent filter issues
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchOrganizations = async () => {
    try {
      const params = new URLSearchParams({
        adminUserId: user.id
      })
      
      const response = await fetch(`/api/organizations?${params}`)
      if (!response.ok) throw new Error('Failed to fetch organizations')
      
      const data = await response.json()
      setOrganizations(data)    } catch (error) {
      console.error('Error fetching organizations:', error)
    }
  }
  const handleUpdateUser = async (formData: FormData) => {
    if (!editingUser) return

    try {
      const organizationIdValue = formData.get('organizationId') as string
      
      const updateData = {
        userId: editingUser.id,
        role: formData.get('role') as string,
        adminLevel: parseInt(formData.get('adminLevel') as string) || 0,
        isActive: formData.get('isActive') === 'on',
        organizationId: organizationIdValue === 'none' ? null : organizationIdValue || undefined,
        adminUserId: user.id
      }

      console.log('Updating user with data:', updateData)

      const response = await fetch('/api/user-api', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updateData)
      })

      if (!response.ok) {
        const errorText = await response.text()
        console.error('Update failed:', response.status, errorText)
        throw new Error('Failed to update user')
      }

      const updatedUser = await response.json()
      console.log('User updated successfully:', updatedUser)

      // If the updated user is the current logged-in user, refresh their session
      if (editingUser.id === user.id) {
        console.log('Updated user is current user, refreshing session...')
        await refreshUser()
      }

      setIsEditDialogOpen(false)
      setEditingUser(null)
      fetchUsers()
    } catch (error) {
      console.error('Error updating user:', error)
    }
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN': return 'destructive'
      case 'ORG_ADMIN': return 'default'
      case 'USER': return 'secondary'
      case 'VIEWER': return 'outline'
      default: return 'secondary'
    }
  }

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'SUPER_ADMIN': return 'Super Admin'
      case 'ORG_ADMIN': return 'Org Admin'
      case 'USER': return 'User'
      case 'VIEWER': return 'Viewer'
      default: return role
    }
  }
  const filteredUsers = (users || []).filter(u => {
    const matchesSearch = !searchTerm || 
      u.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      u.name?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesRole = roleFilter === 'all' || u.role === roleFilter
    
    return matchesSearch && matchesRole
  })

  if (loading) {
    return <div className="flex items-center justify-center h-64">Loading users...</div>
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-white">User Management</h2>
          <p className="text-slate-400">Manage user accounts and roles</p>
        </div>
        <Button disabled>
          <UserPlus className="h-4 w-4 mr-2" />
          Add User
          <span className="ml-2 text-xs">(Coming Soon)</span>
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1 max-w-sm">
              <Label htmlFor="search">Search Users</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="Search by name or email..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="w-48">
              <Label htmlFor="role-filter">Filter by Role</Label>
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Roles</SelectItem>
                  <SelectItem value="SUPER_ADMIN">Super Admin</SelectItem>
                  <SelectItem value="ORG_ADMIN">Org Admin</SelectItem>
                  <SelectItem value="USER">User</SelectItem>
                  <SelectItem value="VIEWER">Viewer</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Users List */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredUsers.map((userItem) => (
          <Card key={userItem.id}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex h-10 w-10 items-center justify-center rounded-full bg-primary/10">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <CardTitle className="text-lg">
                      {userItem.name || userItem.email}
                    </CardTitle>
                    <CardDescription>{userItem.email}</CardDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={getRoleBadgeColor(userItem.role)}>
                    {getRoleDisplayName(userItem.role)}
                  </Badge>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      setEditingUser(userItem)
                      setIsEditDialogOpen(true)
                    }}
                    disabled={
                      user.role !== 'SUPER_ADMIN' && 
                      userItem.adminLevel >= user.adminLevel
                    }
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-slate-400">Status:</span>
                  <Badge variant={userItem.isActive ? "default" : "secondary"}>
                    {userItem.isActive ? "Active" : "Inactive"}
                  </Badge>
                </div>

                {userItem.organization && (
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-slate-400">Organization:</span>
                    <span className="flex items-center gap-1 text-white">
                      <Building className="h-3 w-3" />
                      {userItem.organization.name}
                    </span>
                  </div>
                )}
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">Admin Level:</span>
                  <Badge variant="outline" className="flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    Level {userItem.adminLevel}
                  </Badge>
                </div>
                
                {userItem.webAppPermissions.length > 0 && (
                  <div className="space-y-2">
                    <span className="text-sm text-muted-foreground">App Access:</span>
                    <div className="flex flex-wrap gap-1">
                      {userItem.webAppPermissions.slice(0, 3).map(permission => (
                        <Badge 
                          key={permission.id} 
                          variant="outline" 
                          className="text-xs"
                        >
                          {permission.webApp.title}
                        </Badge>
                      ))}
                      {userItem.webAppPermissions.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{userItem.webAppPermissions.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>
                )}
                
                {userItem.lastLoginAt && (
                  <div className="text-xs text-slate-400">
                    Last login: {new Date(userItem.lastLoginAt).toLocaleDateString()}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredUsers.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Users className="mx-auto h-12 w-12 text-slate-400 mb-4" />
            <h3 className="text-lg font-medium text-white mb-2">No Users Found</h3>
            <p className="text-slate-400">
              {searchTerm || roleFilter !== 'all'
                ? "No users match your search criteria."
                : "No users have been created yet."
              }
            </p>
          </CardContent>
        </Card>
      )}

      {/* Edit User Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user role, permissions, and organization
            </DialogDescription>
          </DialogHeader>
          {editingUser && (
            <form onSubmit={(e) => {
              e.preventDefault()
              handleUpdateUser(new FormData(e.currentTarget))
            }}>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Email</Label>
                    <Input value={editingUser.email} disabled />
                  </div>
                  <div className="space-y-2">
                    <Label>Name</Label>
                    <Input value={editingUser.name || ''} disabled />
                  </div>
                </div>
                  <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="edit-role">Role (determines access level)</Label>
                    <Select name="role" defaultValue={editingUser.role}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {user.role === 'SUPER_ADMIN' && (
                          <SelectItem value="SUPER_ADMIN">Super Admin (Full Access)</SelectItem>
                        )}
                        <SelectItem value="ORG_ADMIN">Org Admin (Organization Access)</SelectItem>
                        <SelectItem value="USER">User (Basic Access)</SelectItem>
                        <SelectItem value="VIEWER">Viewer (Read Only)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                    <div className="space-y-2">
                    <Label htmlFor="edit-adminLevel">Admin Level</Label>
                    <Input 
                      id="edit-adminLevel"
                      name="adminLevel"
                      type="number"
                      min="0"
                      max="999"
                      defaultValue={editingUser.adminLevel}
                    />
                  </div>                </div>
                
                {user.role === 'SUPER_ADMIN' && (
                  <div className="space-y-2">
                    <Label htmlFor="edit-organizationId">Organization</Label>
                    <Select name="organizationId" defaultValue={editingUser.organizationId || 'none'}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select organization" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No Organization</SelectItem>
                        {organizations.map(org => (
                          <SelectItem key={org.id} value={org.id}>
                            {org.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}
                
                <div className="flex items-center space-x-2">
                  <Switch 
                    id="edit-isActive" 
                    name="isActive" 
                    defaultChecked={editingUser.isActive}
                  />
                  <Label htmlFor="edit-isActive">Active User</Label>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">Update User</Button>
              </DialogFooter>
            </form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
