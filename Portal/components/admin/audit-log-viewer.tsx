'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { ClipboardList, Search, Filter, Eye, Calendar, User } from 'lucide-react'

interface AuditLog {
  id: string
  action: string
  details: any
  createdAt: string
  userId: string
  targetId?: string
  user: {
    id: string
    email: string
    name?: string
    role: string
  }
}

interface AuditLogViewerProps {
  user: any
}

export function AuditLogViewer({ user }: AuditLogViewerProps) {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [actionFilter, setActionFilter] = useState<string>('all')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)

  useEffect(() => {
    fetchAuditLogs()
  }, [user.id, page, actionFilter])

  const fetchAuditLogs = async () => {
    try {
      const params = new URLSearchParams({
        adminUserId: user.id,
        limit: '20',
        offset: ((page - 1) * 20).toString()
      })
      
      if (actionFilter !== 'all') {
        params.append('action', actionFilter)
      }
      
      const response = await fetch(`/api/audit-logs?${params}`)
      if (response.ok) {
        const data = await response.json()
        setAuditLogs(data.logs || [])
        setTotalPages(Math.ceil(data.pagination.total / 20))
      }
    } catch (error) {
      console.error('Error fetching audit logs:', error)
    } finally {
      setLoading(false)
    }
  }

  const getActionColor = (action: string) => {
    switch (action) {
      case 'USER_CREATED':
      case 'ORGANIZATION_CREATED':
      case 'WEBAPP_CREATED':
        return 'bg-green-100 text-green-800'
      case 'USER_UPDATED':
      case 'ORGANIZATION_UPDATED':
      case 'WEBAPP_UPDATED':
        return 'bg-blue-100 text-blue-800'
      case 'USER_DELETED':
      case 'ORGANIZATION_DELETED':
      case 'WEBAPP_DELETED':
        return 'bg-red-100 text-red-800'
      case 'PERMISSION_GRANTED':
      case 'PERMISSION_REVOKED':
        return 'bg-purple-100 text-purple-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const filteredLogs = auditLogs.filter(log => 
    log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
    log.user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (log.user.name && log.user.name.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold">Audit Logs</h2>
        <p className="text-muted-foreground">View system activity and changes</p>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <div className="flex-1">
              <Input
                placeholder="Search by action, user, or email..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full"
              />
            </div>
            <Select value={actionFilter} onValueChange={setActionFilter}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="Filter by action" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Actions</SelectItem>
                <SelectItem value="USER_CREATED">User Created</SelectItem>
                <SelectItem value="USER_UPDATED">User Updated</SelectItem>
                <SelectItem value="USER_DELETED">User Deleted</SelectItem>
                <SelectItem value="ORGANIZATION_CREATED">Org Created</SelectItem>
                <SelectItem value="ORGANIZATION_UPDATED">Org Updated</SelectItem>
                <SelectItem value="WEBAPP_CREATED">WebApp Created</SelectItem>
                <SelectItem value="WEBAPP_UPDATED">WebApp Updated</SelectItem>
                <SelectItem value="PERMISSION_GRANTED">Permission Granted</SelectItem>
                <SelectItem value="PERMISSION_REVOKED">Permission Revoked</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ClipboardList className="h-5 w-5" />
            System Activity ({filteredLogs.length} results)
          </CardTitle>
          <CardDescription>Recent system actions and changes</CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
              <p className="text-slate-400 mt-2">Loading audit logs...</p>
            </div>
          ) : filteredLogs.length === 0 ? (
            <div className="text-center py-8">
              <ClipboardList className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-400">No audit logs found</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredLogs.map((log) => (
                <div key={log.id} className="border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                  <div className="flex items-start justify-between gap-4">
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-2">
                        <Badge className={getActionColor(log.action)}>
                          {log.action.replace(/_/g, ' ')}
                        </Badge>
                        <span className="text-sm text-slate-400">
                          <Calendar className="h-4 w-4 inline mr-1" />
                          {formatDate(log.createdAt)}
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-2 text-sm">
                        <User className="h-4 w-4" />
                        <span className="font-medium">{log.user.name || log.user.email}</span>
                        <Badge variant="outline">{log.user.role}</Badge>
                      </div>
                      
                      {log.details && (
                        <div className="text-sm text-muted-foreground">
                          <pre className="whitespace-pre-wrap font-mono text-xs bg-muted p-2 rounded">
                            {JSON.stringify(log.details, null, 2)}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
              
              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex justify-center gap-2 pt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(p => Math.max(1, p - 1))}
                    disabled={page === 1}
                  >
                    Previous
                  </Button>
                  <span className="px-4 py-2 text-sm">
                    Page {page} of {totalPages}
                  </span>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                    disabled={page === totalPages}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
