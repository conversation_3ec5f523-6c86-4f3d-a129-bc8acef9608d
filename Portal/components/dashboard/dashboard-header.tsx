'use client'

import { useState } from 'react'
import { useMsal } from '@azure/msal-react'
import { useAuthStore } from '@/lib/store/authStore'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Icons } from '@/components/ui/icons'
import { ThemeToggle } from '@/components/theme-toggle'
import { getInitials } from '@/lib/utils'

export function DashboardHeader() {
  const { instance } = useMsal()
  const { user, logout } = useAuthStore()
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const handleLogout = async () => {
    setIsLoggingOut(true)
    try {
      // Get the active account to avoid account selection popup
      const activeAccount = instance.getActiveAccount()
      if (activeAccount) {
        await instance.logoutRedirect({
          account: activeAccount,
          postLogoutRedirectUri: window.location.origin
        })
      } else {
        // Fallback: clear cache and redirect manually
        await instance.clearCache()
        logout()
        window.location.href = window.location.origin
      }
    } catch (error) {
      console.error('Logout failed:', error)
      // Fallback: force logout locally
      logout()
      window.location.href = window.location.origin
    } finally {
      setIsLoggingOut(false)
    }
  }

  if (!user) return null

  return (
    <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto px-4">
        <div className="flex h-16 items-center justify-between">          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-3">
              <img 
                src="/quantumrhino.svg" 
                alt="QuantumRhino" 
                className="h-8 w-auto"
              />
              <span className="font-bold text-xl">Portal</span>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <ThemeToggle />
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-8 w-8 rounded-full">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none">{user.name}</p>
                    <p className="text-xs leading-none text-muted-foreground">
                      {user.email}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Icons.user className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Icons.settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleLogout} disabled={isLoggingOut}>
                  <Icons.logout className="mr-2 h-4 w-4" />
                  <span>{isLoggingOut ? 'Signing out...' : 'Sign out'}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  )
}
