'use client'

import { useState, useEffect } from 'react'
import { useMsal } from '@azure/msal-react'
import { useAuthStore } from '@/lib/store/authStore'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { Icons } from '@/components/ui/icons'
import { getInitials } from '@/lib/utils'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

export function DashboardHeader() {
  const { instance } = useMsal()
  const { user, logout } = useAuthStore()
  const [isLoggingOut, setIsLoggingOut] = useState(false)
  const [scrolled, setScrolled] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])
  const handleLogout = async () => {
    setIsLoggingOut(true)
    try {
      // Get the active account to avoid account selection popup
      const activeAccount = instance.getActiveAccount()
      if (activeAccount) {
        await instance.logoutRedirect({
          account: activeAccount,
          postLogoutRedirectUri: window.location.origin
        })
      } else {
        // Fallback: clear cache and redirect manually
        await instance.clearCache()
        logout()
        window.location.href = window.location.origin
      }
    } catch (error) {
      console.error('Logout failed:', error)
      // Fallback: force logout locally
      logout()
      window.location.href = window.location.origin
    } finally {
      setIsLoggingOut(false)
    }
  }

  if (!user) return null

  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
      scrolled
        ? 'bg-slate-900/80 backdrop-blur-2xl border-b border-white/10 shadow-2xl'
        : 'bg-transparent'
    }`}>
      <div className="container mx-auto px-6">
        <div className="flex items-center justify-between h-24 py-4 px-4">
          {/* Logo */}
          <Link href="/dashboard" className="flex items-center group">
            <img
              src="https://cdn.prod.website-files.com/6552a024805ad417ae76ef91/6552a024805ad417ae76efba_top.svg"
              alt="QuantumRhino"
              className="h-8 w-auto group-hover:scale-105 transition-all duration-300"
              onError={(e) => {
                // Fallback to text if image fails to load
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'block';
              }}
            />
            <div className="text-white font-black text-2xl bg-gradient-to-r from-white to-slate-300 bg-clip-text text-transparent group-hover:scale-105 transition-all duration-300 hidden ml-3">
              Portal
            </div>
          </Link>



          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 w-10 rounded-full bg-white/10 hover:bg-white/20 border border-white/20 hover:border-white/30 transition-all duration-300">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold">
                      {getInitials(user.name)}
                    </AvatarFallback>
                  </Avatar>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-56" align="end" forceMount>
                <DropdownMenuLabel className="font-normal">
                  <div className="flex flex-col space-y-1">
                    <p className="text-sm font-medium leading-none text-white">{user.name}</p>
                    <p className="text-xs leading-none text-slate-400">
                      {user.email}
                    </p>
                    <p className="text-xs leading-none text-slate-500 capitalize">
                      {user.role?.toLowerCase().replace('_', ' ')}
                    </p>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-white/10" />
                <DropdownMenuItem>
                  <Icons.user className="mr-2 h-4 w-4" />
                  <span>Profile</span>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Icons.settings className="mr-2 h-4 w-4" />
                  <span>Settings</span>
                </DropdownMenuItem>
                {(user.role === 'SUPER_ADMIN' || user.role === 'ORG_ADMIN') && (
                  <DropdownMenuItem onClick={() => router.push('/admin')}>
                    <Icons.shield className="mr-2 h-4 w-4" />
                    <span>Admin Panel</span>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={handleLogout}
                  disabled={isLoggingOut}
                  className="text-red-400 hover:text-red-300 hover:bg-red-500/10 focus:bg-red-500/10 focus:text-red-300"
                >
                  <Icons.logout className="mr-2 h-4 w-4" />
                  <span>{isLoggingOut ? 'Signing out...' : 'Sign out'}</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </nav>
  )
}
