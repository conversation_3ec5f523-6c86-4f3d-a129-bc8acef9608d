'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'
import { formatDate } from '@/lib/utils'

interface Activity {
  id: string
  action: string
  target: string
  user: string
  timestamp: string
  status: 'success' | 'error' | 'pending'
}

export function RecentActivity() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchActivity = async () => {
      try {
        const response = await fetch('/api/activity')
        if (!response.ok) {
          throw new Error('Failed to fetch activity')
        }
        const result = await response.json()
        if (result.success) {
          setActivities(result.data)
        } else {
          throw new Error(result.error || 'Failed to fetch activity')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch activity')
        console.error('Error fetching activity:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchActivity()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'pending':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <LoadingSpinner />
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center text-red-600">
            Error loading activity: {error}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4">
              <div className={`w-2 h-2 rounded-full mt-2 ${
                activity.status === 'success' 
                  ? 'bg-green-500' 
                  : activity.status === 'error'
                  ? 'bg-red-500'
                  : 'bg-yellow-500'
              }`} />
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium">
                    <span className={getStatusColor(activity.status)}>
                      {activity.action}
                    </span>{' '}
                    <span className="text-muted-foreground">{activity.target}</span>
                  </p>
                  <time className="text-xs text-muted-foreground">
                    {formatDate(new Date(activity.timestamp))}
                  </time>
                </div>
                <p className="text-xs text-muted-foreground">
                  by {activity.user}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
