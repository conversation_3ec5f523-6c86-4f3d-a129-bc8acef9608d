'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Icons } from '@/components/ui/icons'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface StatsData {
  runningApps: number
  totalDeployments: number
  successRate: number
  activeUsers: number
  deploymentsThisMonth: number
  deploymentsLastMonth: number
}

export function DashboardStats() {
  const [stats, setStats] = useState<StatsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/stats')
        if (!response.ok) {
          throw new Error('Failed to fetch stats')
        }
        const result = await response.json()
        if (result.success) {
          setStats(result.data)
        } else {
          throw new Error(result.error || 'Failed to fetch stats')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch stats')
        console.error('Error fetching stats:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <Card key={index}>
            <CardContent className="p-6">
              <LoadingSpinner />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error || !stats) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="col-span-full">
          <CardContent className="p-6 text-center text-red-600">
            Error loading stats: {error}
          </CardContent>
        </Card>
      </div>
    )
  }

  const statsConfig = [
    {
      title: 'Running Apps',
      value: stats.runningApps.toString(),
      change: `${stats.deploymentsThisMonth - stats.deploymentsLastMonth >= 0 ? '+' : ''}${stats.deploymentsThisMonth - stats.deploymentsLastMonth} from last month`,
      icon: Icons.container,
      color: 'text-green-600',
    },
    {
      title: 'Total Deployments',
      value: stats.totalDeployments.toString(),
      change: `+${stats.deploymentsThisMonth} this month`,
      icon: Icons.activity,
      color: 'text-blue-600',
    },
    {
      title: 'Success Rate',
      value: `${stats.successRate}%`,
      change: stats.successRate >= 98 ? '+1.2% from last month' : 'Needs attention',
      icon: Icons.shield,
      color: 'text-purple-600',
    },
    {
      title: 'Active Users',
      value: stats.activeUsers.toString(),
      change: '+4 from last month',
      icon: Icons.user,
      color: 'text-orange-600',
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statsConfig.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className={`h-4 w-4 ${stat.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">
              {stat.change}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
