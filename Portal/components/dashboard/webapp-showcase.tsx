'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, Lock, Globe, Settings } from 'lucide-react'
import { useAuthStore } from '@/lib/store/authStore'

interface WebApp {
  id: string
  name: string
  title: string
  description?: string
  image?: string
  url: string
  category?: string
  tags: string[]
  isActive: boolean
  requiresAuth: boolean
  organization?: {
    id: string
    name: string
  }
  createdBy: {
    id: string
    name?: string
    email: string
  }
}

interface WebAppShowcaseProps {
  isAdminView?: boolean
  onEditWebApp?: (webApp: WebApp) => void
}

export function WebAppShowcase({ isAdminView = false, onEditWebApp }: WebAppShowcaseProps) {  const [webapps, setWebapps] = useState<WebApp[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  
  const { user } = useAuthStore()
  
  useEffect(() => {
    console.log('WebAppShowcase user:', user, 'isAdminView:', isAdminView)
    if (user && user.id) {
      fetchWebApps()
    } else {
      console.log('No user or user.id available for webapps')
      setLoading(false)
    }
  }, [user?.id, isAdminView]) // eslint-disable-line react-hooks/exhaustive-deps

  const fetchWebApps = async () => {
    if (!user || !user.id) {
      console.log('Cannot fetch webapps: user or user.id is missing', { user })
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      const params = new URLSearchParams({
        userId: user.id,
        showAll: isAdminView.toString()
      })
      
      console.log('Fetching webapps with params:', params.toString())
      const response = await fetch(`/api/webapps?${params}`)
      console.log('WebApps response status:', response.status)
      
      if (!response.ok) {
        const errorText = await response.text()
        console.error('WebApps API error:', response.status, errorText)
        throw new Error('Failed to fetch webapps')
      }
      
      const data = await response.json()
      console.log('WebApps data received:', data)
      setWebapps(data)
    } catch (err) {
      console.error('Error fetching webapps:', err)
      setError(err instanceof Error ? err.message : 'Failed to load webapps')
    } finally {
      setLoading(false)
    }
  }

  const handleWebAppAccess = async (webapp: WebApp) => {
    if (!webapp.requiresAuth) {
      // Open webapp directly if no auth required
      window.open(webapp.url, '_blank')
      return
    }

    // For authenticated webapps, we could implement additional checks here
    // For now, just open the webapp
    window.open(webapp.url, '_blank')
  }

  const categories = ['all', ...Array.from(new Set(webapps.map(w => w.category).filter((cat): cat is string => Boolean(cat))))]
  const filteredWebapps = selectedCategory === 'all' 
    ? webapps 
    : webapps.filter(w => w.category === selectedCategory)

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <div className="h-48 bg-gray-200 rounded-t-lg"></div>
            <CardHeader>
              <div className="h-6 bg-gray-200 rounded mb-2"></div>
              <div className="h-4 bg-gray-200 rounded"></div>
            </CardHeader>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-500 mb-4">{error}</p>
        <Button onClick={fetchWebApps}>Try Again</Button>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      {categories.length > 1 && (
        <div className="flex flex-wrap gap-2">
          {categories.map(category => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              className="capitalize"
            >
              {category}
            </Button>
          ))}
        </div>
      )}

      {/* Webapp Grid */}
      {filteredWebapps.length === 0 ? (
        <div className="text-center py-12">
          <Globe className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Web Applications</h3>
          <p className="text-gray-500">
            {isAdminView 
              ? "No webapps have been created yet. Create your first webapp to get started."
              : "You don't have access to any webapps yet. Contact your administrator for access."
            }
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWebapps.map((webapp) => (
            <Card key={webapp.id} className="group hover:shadow-lg transition-shadow">
              {/* Webapp Image */}
              <div className="relative h-48 overflow-hidden rounded-t-lg bg-gradient-to-br from-blue-500 to-purple-600">
                {webapp.image ? (
                  <img 
                    src={webapp.image} 
                    alt={webapp.title}
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <Globe className="h-16 w-16 text-white/80" />
                  </div>
                )}
                
                {/* Status Badges */}
                <div className="absolute top-3 right-3 flex gap-2">
                  {webapp.requiresAuth && (
                    <Badge variant="secondary" className="bg-white/90 text-gray-800">
                      <Lock className="w-3 h-3 mr-1" />
                      Auth Required
                    </Badge>
                  )}
                  {!webapp.isActive && (
                    <Badge variant="destructive">Inactive</Badge>
                  )}
                </div>

                {/* Admin Controls */}
                {isAdminView && onEditWebApp && (
                  <div className="absolute top-3 left-3">
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={() => onEditWebApp(webapp)}
                      className="bg-white/90 hover:bg-white"
                    >
                      <Settings className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </div>

              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span className="truncate">{webapp.title}</span>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => handleWebAppAccess(webapp)}
                    disabled={!webapp.isActive}
                    className="shrink-0"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                </CardTitle>
                
                {webapp.description && (
                  <CardDescription className="line-clamp-2">
                    {webapp.description}
                  </CardDescription>
                )}
              </CardHeader>

              <CardContent>
                <div className="space-y-3">
                  {/* Tags */}
                  {webapp.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {webapp.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                      {webapp.tags.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{webapp.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* Organization & Creator Info */}
                  {isAdminView && (
                    <div className="text-xs text-gray-500 space-y-1">
                      {webapp.organization && (
                        <div>Org: {webapp.organization.name}</div>
                      )}
                      <div>Created by: {webapp.createdBy.name || webapp.createdBy.email}</div>
                    </div>
                  )}

                  {/* Access Button */}
                  <Button 
                    className="w-full"
                    onClick={() => handleWebAppAccess(webapp)}
                    disabled={!webapp.isActive}
                  >
                    {webapp.isActive ? 'Launch App' : 'Inactive'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
