'use client'

import { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Icons } from '@/components/ui/icons'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

interface Application {
  id: string
  name: string
  description: string
  status: 'running' | 'stopped' | 'error'
  url?: string | null
  lastDeployed: string
  dockerImage?: string
  port?: number
}

export function ApplicationGrid() {
  const [applications, setApplications] = useState<Application[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchApplications = async () => {
      try {
        const response = await fetch('/api/applications')
        if (!response.ok) {
          throw new Error('Failed to fetch applications')
        }
        const result = await response.json()
        if (result.success) {
          // Format the dates for display
          const formattedApps = result.data.map((app: any) => ({
            ...app,
            lastDeployed: new Date(app.lastDeployed).toLocaleString(),
          }))
          setApplications(formattedApps)
        } else {
          throw new Error(result.error || 'Failed to fetch applications')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch applications')
        console.error('Error fetching applications:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchApplications()
  }, [])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-600 bg-green-100'
      case 'stopped':
        return 'text-gray-600 bg-gray-100'
      case 'error':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return Icons.play
      case 'stopped':
        return Icons.pause
      case 'error':
        return Icons.alertCircle
      default:
        return Icons.pause
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Applications</h2>
          <Button>
            <Icons.plus className="mr-2 h-4 w-4" />
            New App
          </Button>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {[...Array(3)].map((_, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <LoadingSpinner />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">Applications</h2>
          <Button>
            <Icons.plus className="mr-2 h-4 w-4" />
            New App
          </Button>
        </div>
        <Card>
          <CardContent className="p-6 text-center text-red-600">
            Error loading applications: {error}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Applications</h2>
        <Button>
          <Icons.plus className="mr-2 h-4 w-4" />
          New App
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {applications.map((app) => {
          const StatusIcon = getStatusIcon(app.status)
          return (
            <Card key={app.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{app.name}</CardTitle>
                  <span className={`px-2 py-1 text-xs rounded-full flex items-center gap-1 ${getStatusColor(app.status)}`}>
                    <StatusIcon className="h-3 w-3" />
                    {app.status}
                  </span>
                </div>
                <CardDescription>{app.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Last deployed:</span>
                    <span>{app.lastDeployed}</span>
                  </div>
                  
                  {app.dockerImage && (
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Image:</span>
                      <span className="font-mono text-xs bg-muted px-2 py-1 rounded">
                        {app.dockerImage}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex gap-2 pt-2">
                    {app.url && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => window.open(app.url!, '_blank')}
                      >
                        <Icons.externalLink className="mr-2 h-3 w-3" />
                        Open
                      </Button>
                    )}
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        // TODO: Implement restart functionality
                        console.log('Restart app:', app.id)
                      }}
                    >
                      <Icons.refresh className="mr-2 h-3 w-3" />
                      Restart
                    </Button>
                    
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => {
                        // TODO: Implement view logs functionality
                        console.log('View logs:', app.id)
                      }}
                    >
                      <Icons.eye className="mr-2 h-3 w-3" />
                      Logs
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
