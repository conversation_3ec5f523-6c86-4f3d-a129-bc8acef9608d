const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function addTestWebApp() {
  try {
    // Find the first user to be the creator
    const firstUser = await prisma.user.findFirst()
    
    if (!firstUser) {
      console.log('No users found in database')
      return
    }

    console.log('Found user:', firstUser.email)

    // Create a test webapp
    const webapp = await prisma.webApp.create({
      data: {
        name: 'test-app',
        title: 'Test Application',
        description: 'A test application for the dashboard',
        url: 'https://example.com',
        category: 'Productivity',
        tags: ['test', 'example'],
        requiresAuth: false,
        isActive: true,
        order: 1,
        createdById: firstUser.id,
        organizationId: firstUser.organizationId
      }
    })

    console.log('Created webapp:', webapp)

    // Check the count
    const count = await prisma.webApp.count()
    console.log('Total webapps in database:', count)

  } catch (error) {
    console.error('Error:', error)
  } finally {
    await prisma.$disconnect()
  }
}

addTestWebApp()
