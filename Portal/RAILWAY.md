# Railway Deployment Template

This template contains all the configuration needed to deploy the QuantomRhino Portal to Railway.

## Quick Deploy to Railway

[![Deploy on Railway](https://railway.app/button.svg)](https://railway.app/template/your-template-id)

## Manual Deployment

1. **Connect Repository**
   - Connect your GitHub repository to Railway
   - Railway will auto-detect this as a Next.js project

2. **Add PostgreSQL Database**
   - In Railway dashboard, click "New" → "Database" → "PostgreSQL"
   - DATABASE_URL will be automatically configured

3. **Environment Variables**
   Set these variables in Railway dashboard:

   ```
   NEXT_PUBLIC_AZURE_AD_CLIENT_ID=your-azure-ad-client-id
   NEXT_PUBLIC_AZURE_AD_TENANT_ID=your-azure-ad-tenant-id
   AZURE_AD_CLIENT_SECRET=your-azure-ad-client-secret
   NEXTAUTH_SECRET=your-nextauth-secret-32-chars
   NEXT_PUBLIC_APP_NAME=QuantomRhino Portal
   NEXT_PUBLIC_COMPANY_NAME=QuantomRhino
   ```

4. **Deploy**
   - Railway will automatically build and deploy
   - Get your deployment URL from Railway dashboard
   - Update Azure AD redirect URI with your Railway URL

5. **Initialize Database**
   ```bash
   railway run npx prisma db push
   ```

## Railway Services Configuration

### Web Service (Next.js App)
- **Build Command**: `npm run build`
- **Start Command**: `npm start`
- **Environment**: Node.js 18+
- **Port**: 3000 (auto-detected)

### Database Service (PostgreSQL)
- **Engine**: PostgreSQL 15
- **Auto-configured with DATABASE_URL**

## Environment Variables Reference

| Variable | Description | Required |
|----------|-------------|----------|
| `DATABASE_URL` | PostgreSQL connection string | ✅ (Auto-set by Railway) |
| `NEXT_PUBLIC_AZURE_AD_CLIENT_ID` | Azure AD Application ID | ✅ |
| `NEXT_PUBLIC_AZURE_AD_TENANT_ID` | Azure AD Tenant ID | ✅ |
| `AZURE_AD_CLIENT_SECRET` | Azure AD Client Secret | ✅ |
| `NEXTAUTH_SECRET` | Random 32-character string | ✅ |
| `NEXT_PUBLIC_REDIRECT_URI` | Your Railway app URL | ❌ (Auto-set) |
| `NEXTAUTH_URL` | Your Railway app URL | ❌ (Auto-set) |

## Custom Domain Setup

1. **Add Custom Domain in Railway**
   - Go to your service settings
   - Add your custom domain
   - Configure DNS as instructed

2. **Update Azure AD Configuration**
   - Update redirect URI in Azure AD to use your custom domain
   - Update environment variables if needed

## Monitoring and Logs

- **Application Logs**: Available in Railway dashboard
- **Metrics**: Railway provides built-in metrics
- **Health Checks**: Automatic health monitoring

## Scaling

Railway automatically scales based on traffic. For high-traffic applications:

- **Horizontal Scaling**: Railway handles this automatically
- **Database Scaling**: Upgrade PostgreSQL plan as needed
- **CDN**: Consider adding Cloudflare for static assets

## Backup and Recovery

- **Database Backups**: Railway PostgreSQL includes automatic backups
- **Code Backups**: Stored in your GitHub repository
- **Environment Variables**: Document in secure location

## Security Best Practices

1. **Environment Variables**: Never commit secrets to Git
2. **HTTPS Only**: Railway provides HTTPS by default
3. **Database Access**: Restricted to Railway network
4. **Authentication**: Uses Azure AD for secure access
5. **Regular Updates**: Keep dependencies updated
