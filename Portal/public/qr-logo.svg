<svg width="800" height="600" viewBox="0 0 800 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Geometric rhino silhouette with gradient colors -->
  <defs>
    <linearGradient id="rhinoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF6B35;stop-opacity:1" />
      <stop offset="20%" style="stop-color:#F7931E;stop-opacity:1" />
      <stop offset="40%" style="stop-color:#FFD100;stop-opacity:1" />
      <stop offset="60%" style="stop-color:#E91E63;stop-opacity:1" />
      <stop offset="80%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#3F51B5;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Rhino body - main geometric shape -->
  <polygon points="150,450 200,400 300,380 450,390 600,420 650,480 600,520 500,530 350,520 200,500 150,450" fill="url(#rhinoGradient)" opacity="0.9"/>
  
  <!-- Rhino head -->
  <polygon points="100,350 150,300 250,290 300,320 280,380 200,390 120,380 100,350" fill="url(#rhinoGradient)" opacity="0.8"/>
  
  <!-- Horn -->
  <polygon points="80,280 120,250 140,290 100,320 80,280" fill="url(#rhinoGradient)"/>
  
  <!-- Legs -->
  <polygon points="200,520 220,580 240,580 230,520 200,520" fill="url(#rhinoGradient)" opacity="0.7"/>
  <polygon points="280,520 300,580 320,580 310,520 280,520" fill="url(#rhinoGradient)" opacity="0.7"/>
  <polygon points="450,520 470,580 490,580 480,520 450,520" fill="url(#rhinoGradient)" opacity="0.7"/>
  <polygon points="530,520 550,580 570,580 560,520 530,520" fill="url(#rhinoGradient)" opacity="0.7"/>
  
  <!-- Tail -->
  <polygon points="650,450 700,420 720,440 680,470 650,450" fill="url(#rhinoGradient)" opacity="0.6"/>
  
  <!-- Additional geometric details for modern look -->
  <polygon points="300,350 350,330 380,360 340,380 300,350" fill="url(#rhinoGradient)" opacity="0.5"/>
  <polygon points="450,380 500,370 520,400 480,410 450,380" fill="url(#rhinoGradient)" opacity="0.5"/>
</svg>
