import { NextRequest, NextResponse } from 'next/server';

export function middleware(request: NextRequest) {
  // For now, we'll use a simple header-based auth
  // In production, you'd integrate with your Portal's auth system
  
  if (request.nextUrl.pathname.startsWith('/api/')) {
    // Check for user ID in headers
    const userId = request.headers.get('x-user-id');
    
    if (!userId) {
      // For development, create a default user ID
      if (process.env.NODE_ENV === 'development') {
        const response = NextResponse.next();
        response.headers.set('x-user-id', 'dev-user-123');
        return response;
      }
      
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    '/api/:path*'
  ]
};
