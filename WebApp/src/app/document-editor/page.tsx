'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '../components/loading-spinner';

interface TemplateData {
  id: string;
  name: string;
  markdownContent: string;
  extractedFields: string[];
}

interface DocumentSection {
  id: string;
  title: string;
  content: string;
  fields: Array<{
    name: string;
    placeholder: string;
    value: string;
    type: 'text' | 'number' | 'date' | 'email' | 'currency';
    required: boolean;
    aiSuggestion?: string;
  }>;
  isComplete: boolean;
}

const DocumentEditorPage = () => {
  const [templateData, setTemplateData] = useState<TemplateData | null>(null);
  const [sections, setSections] = useState<DocumentSection[]>([]);
  const [currentSectionIndex, setCurrentSectionIndex] = useState(0);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [documentPreview, setDocumentPreview] = useState('');
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const stored = localStorage.getItem('uploadedTemplate');
    if (stored) {
      const template = JSON.parse(stored);
      setTemplateData(template);
      analyzeDocument(template);
    } else {
      router.push('/reference-upload');
    }
  }, [router]);

  const analyzeDocument = async (template: TemplateData) => {
    setIsAnalyzing(true);
    try {
      const response = await fetch('/api/document/analyze', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: template.id
        }),
      });

      if (!response.ok) throw new Error('Failed to analyze document');

      const result = await response.json();
      setSections(result.sections);
      setDocumentPreview(result.preview);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to analyze document');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleFieldChange = (fieldName: string, value: string) => {
    setSections(prev => prev.map((section, index) => {
      if (index === currentSectionIndex) {
        const updatedFields = section.fields.map(field => 
          field.name === fieldName ? { ...field, value } : field
        );
        return {
          ...section,
          fields: updatedFields,
          isComplete: updatedFields.every(f => !f.required || f.value.trim() !== '')
        };
      }
      return section;
    }));

    // Auto-generate AI suggestions for related fields
    generateAISuggestions(fieldName, value);
  };

  const generateAISuggestions = async (changedField: string, value: string) => {
    if (!value.trim()) return;

    try {
      const response = await fetch('/api/document/suggest', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          changedField,
          value,
          currentSection: sections[currentSectionIndex],
          allSections: sections
        }),
      });

      if (response.ok) {
        const suggestions = await response.json();
        setSections(prev => prev.map((section, index) => {
          if (index === currentSectionIndex) {
            return {
              ...section,
              fields: section.fields.map(field => ({
                ...field,
                aiSuggestion: suggestions[field.name] || field.aiSuggestion
              }))
            };
          }
          return section;
        }));
      }
    } catch (error) {
      console.error('Failed to generate AI suggestions:', error);
    }
  };

  const applyAISuggestion = (fieldName: string, suggestion: string) => {
    handleFieldChange(fieldName, suggestion);
  };

  const nextSection = () => {
    if (currentSectionIndex < sections.length - 1) {
      setCurrentSectionIndex(currentSectionIndex + 1);
    }
  };

  const prevSection = () => {
    if (currentSectionIndex > 0) {
      setCurrentSectionIndex(currentSectionIndex - 1);
    }
  };

  const generateFinalDocument = async () => {
    setIsGenerating(true);
    try {
      const response = await fetch('/api/document/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: templateData?.id,
          sections: sections
        }),
      });

      if (!response.ok) throw new Error('Failed to generate document');

      const result = await response.json();
      localStorage.setItem('finalDocument', JSON.stringify(result));
      router.push('/document-preview');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate document');
    } finally {
      setIsGenerating(false);
    }
  };

  if (isAnalyzing) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md">
          <LoadingSpinner size="lg" color="white" />
          <h2 className="text-2xl font-bold text-white mt-6 mb-2">Analyzing Your Document</h2>
          <p className="text-slate-300 mb-4">Organizing fields and creating sections...</p>
          <div className="bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
            <p className="text-blue-300 text-sm">⚡ Almost ready! This takes just a moment.</p>
          </div>
        </div>
      </div>
    );
  }

  if (!templateData || sections.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <LoadingSpinner size="lg" color="white" text="Loading document..." />
      </div>
    );
  }

  const currentSection = sections[currentSectionIndex];
  const completedSections = sections.filter(s => s.isComplete).length;
  const progressPercentage = (completedSections / sections.length) * 100;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-6 shadow-2xl">
            <span className="text-2xl font-bold text-white">✏️</span>
          </div>
          
          <h1 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent mb-4">
            Document Editor
          </h1>
          
          <p className="text-lg text-slate-300 max-w-2xl mx-auto mb-6">
            Editing: <span className="text-blue-400 font-semibold">{templateData.name}</span>
          </p>

          {/* Progress Bar */}
          <div className="max-w-md mx-auto mb-8">
            <div className="flex justify-between text-sm text-slate-400 mb-2">
              <span>Progress</span>
              <span>{completedSections}/{sections.length} sections complete</span>
            </div>
            <div className="w-full bg-slate-700 rounded-full h-3">
              <div 
                className="bg-gradient-to-r from-blue-500 to-purple-600 h-3 rounded-full transition-all duration-500"
                style={{ width: `${progressPercentage}%` }}
              ></div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-7xl mx-auto">
          {/* Editor Panel */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-bold text-white mb-1">{currentSection.title}</h2>
                <p className="text-slate-300 text-sm">Section {currentSectionIndex + 1} of {sections.length}</p>
              </div>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                currentSection.isComplete 
                  ? 'bg-green-500/20 border border-green-400/30 text-green-300'
                  : 'bg-yellow-500/20 border border-yellow-400/30 text-yellow-300'
              }`}>
                {currentSection.isComplete ? '✓ Complete' : '⏳ In Progress'}
              </div>
            </div>

            <div className="space-y-6">
              {currentSection.fields.map((field, index) => (
                <div key={field.name} className="space-y-3">
                  <label className="block text-sm font-medium text-white">
                    {field.placeholder}
                    {field.required && <span className="text-red-400 ml-1">*</span>}
                  </label>
                  
                  <div className="relative">
                    <input
                      type={field.type}
                      value={field.value}
                      onChange={(e) => handleFieldChange(field.name, e.target.value)}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                      placeholder={`Enter ${field.placeholder.toLowerCase()}`}
                    />
                    
                    {field.aiSuggestion && field.value !== field.aiSuggestion && (
                      <button
                        onClick={() => applyAISuggestion(field.name, field.aiSuggestion!)}
                        className="absolute right-2 top-2 px-3 py-1 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-lg text-xs hover:bg-blue-500/30 transition-all duration-300"
                      >
                        🤖 Use AI: {field.aiSuggestion.substring(0, 20)}...
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center mt-8 pt-6 border-t border-white/10">
              <button
                onClick={prevSection}
                disabled={currentSectionIndex === 0}
                className="px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                ← Previous
              </button>

              {currentSectionIndex === sections.length - 1 ? (
                <button
                  onClick={generateFinalDocument}
                  disabled={!sections.every(s => s.isComplete) || isGenerating}
                  className="px-8 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isGenerating ? (
                    <LoadingSpinner size="sm" color="white" text="Generating..." />
                  ) : (
                    <>🚀 Generate Final Document</>
                  )}
                </button>
              ) : (
                <button
                  onClick={nextSection}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300"
                >
                  Next →
                </button>
              )}
            </div>
          </div>

          {/* Live Preview Panel */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">Live Preview</h3>
              <span className="px-3 py-1 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-full text-sm font-medium">
                🔄 Real-time
              </span>
            </div>
            
            <div className="bg-white rounded-2xl p-6 shadow-xl max-h-96 overflow-auto">
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{
                  __html: documentPreview
                    .replace(/\[([A-Z_]+)\]/g, (match, fieldName) => {
                      // Find the field value across all sections
                      for (const section of sections) {
                        const field = section.fields.find(f => f.name === fieldName);
                        if (field && field.value) {
                          return `<span style="background-color: #e3f2fd; padding: 2px 4px; border-radius: 3px; font-weight: bold;">${field.value}</span>`;
                        }
                      }
                      return `<span style="background-color: #ffebee; padding: 2px 4px; border-radius: 3px; color: #c62828;">${match}</span>`;
                    })
                }}
              />
            </div>
            
            <div className="mt-4 text-center">
              <p className="text-slate-400 text-sm">
                Preview updates as you type • Blue highlights show your edits
              </p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="max-w-4xl mx-auto mt-8 p-6 bg-red-500/10 border border-red-400/30 rounded-2xl">
            <div className="flex items-start">
              <div className="text-red-400 mr-4 text-2xl">⚠️</div>
              <div>
                <h3 className="text-red-300 font-bold text-lg mb-2">Error</h3>
                <p className="text-red-200">{error}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentEditorPage;
