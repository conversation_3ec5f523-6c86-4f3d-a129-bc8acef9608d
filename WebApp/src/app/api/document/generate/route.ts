import { NextRequest, NextResponse } from 'next/server';

// Extract field values from sections
function extractFieldValues(sections: any[]): { [key: string]: string } {
  const fieldValues: { [key: string]: string } = {};

  sections.forEach(section => {
    section.fields.forEach((field: any) => {
      if (field.value) {
        fieldValues[field.name] = field.value;
      }
    });
  });

  return fieldValues;
}



export async function POST(request: NextRequest) {
  try {
    const { templateId, sections } = await request.json();

    if (!templateId || !sections) {
      return NextResponse.json({ error: 'Missing data' }, { status: 400 });
    }

    // Extract field values
    const fieldValues = extractFieldValues(sections);

    // Calculate basic stats
    const totalFields = sections.reduce((total: number, section: any) => total + section.fields.length, 0);
    const completedFields = sections.reduce((total: number, section: any) =>
      total + section.fields.filter((f: any) => f.value).length, 0
    );

    return NextResponse.json({
      id: templateId,
      fieldValues: fieldValues,
      html: '<div class="p-4">Document ready for download</div>',
      stats: {
        totalFields,
        completedFields,
        completionPercentage: Math.round((completedFields / totalFields) * 100)
      },
      sections: sections
    });

  } catch (error) {
    console.error('Generation error:', error);
    return NextResponse.json(
      { error: 'Generation failed' },
      { status: 500 }
    );
  }
}

// Generate HTML preview with field values filled in
function generateFilledHTMLPreview(originalHtml: string, fieldValues: { [key: string]: string }): string {
  let filledHtml = originalHtml;

  // Replace all placeholders with actual values
  for (const [fieldName, value] of Object.entries(fieldValues)) {
    const regex = new RegExp(`\\[${fieldName}\\]`, 'g');
    filledHtml = filledHtml.replace(regex, `<span class="filled-field" style="background-color: #e3f2fd; padding: 2px 4px; border-radius: 3px; font-weight: bold;">${value}</span>`);
  }

  // Style unfilled placeholders
  filledHtml = filledHtml.replace(/\[([A-Z_]+)\]/g, '<span class="unfilled-field" style="background-color: #ffebee; padding: 2px 4px; border-radius: 3px; color: #c62828;">[$1]</span>');

  return `
    <div class="document-preview" style="
      font-family: 'Times New Roman', serif;
      line-height: 1.6;
      color: #333;
      max-width: 8.5in;
      margin: 0 auto;
      padding: 1in;
      background: white;
      box-shadow: 0 4px 6px rgba(0,0,0,0.1);
      border-radius: 8px;
    ">
      <style>
        .filled-field {
          background-color: #e3f2fd !important;
          padding: 2px 4px !important;
          border-radius: 3px !important;
          font-weight: bold !important;
        }
        .unfilled-field {
          background-color: #ffebee !important;
          padding: 2px 4px !important;
          border-radius: 3px !important;
          color: #c62828 !important;
        }
      </style>
      ${filledHtml}
    </div>
  `;
}
