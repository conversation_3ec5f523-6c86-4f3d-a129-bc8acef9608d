import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile, unlink } from 'fs/promises';
import { join } from 'path';

interface SavedSOW {
  id: string;
  title: string;
  clientName: string;
  projectName: string;
  templateName: string;
  templateId?: string;
  markdown: string;
  createdAt: string;
  status: 'draft' | 'final';
  fileSize?: number;
}

// GET - Get specific SOW details
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const sowId = resolvedParams.id;

    if (!sowId) {
      return NextResponse.json(
        { error: 'SOW ID is required' },
        { status: 400 }
      );
    }

    const uploadsDir = join(process.cwd(), 'uploads');
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);

    try {
      const sowData = await readFile(sowFilePath, 'utf-8');
      const sow: SavedSOW = JSON.parse(sowData);

      return NextResponse.json(sow);
    } catch (error) {
      return NextResponse.json(
        { error: 'SOW not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('SOW fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// PUT - Update a specific SOW
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const sowId = resolvedParams.id;

    if (!sowId) {
      return NextResponse.json(
        { error: 'SOW ID is required' },
        { status: 400 }
      );
    }

    const updatedSOWData = await request.json();

    const uploadsDir = join(process.cwd(), 'uploads');
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);
    const sowsListPath = join(uploadsDir, 'sows.json');

    // Update individual SOW file
    try {
      const existingSOWData = await readFile(sowFilePath, 'utf-8');
      const existingSOW: SavedSOW = JSON.parse(existingSOWData);

      // Merge the updates with existing data
      const updatedSOW: SavedSOW = {
        ...existingSOW,
        ...updatedSOWData,
        id: sowId, // Ensure ID doesn't change
        createdAt: existingSOW.createdAt, // Preserve creation date
      };

      // Save updated SOW file
      await writeFile(sowFilePath, JSON.stringify(updatedSOW, null, 2));

      // Update SOWs list
      try {
        const sowsData = await readFile(sowsListPath, 'utf-8');
        let sowsList: SavedSOW[] = JSON.parse(sowsData);

        const sowIndex = sowsList.findIndex(sow => sow.id === sowId);
        if (sowIndex !== -1) {
          sowsList[sowIndex] = updatedSOW;
          await writeFile(sowsListPath, JSON.stringify(sowsList, null, 2));
        }
      } catch (error) {
        console.warn('Could not update SOWs list:', error);
      }

      console.log('SOW updated successfully:', sowId);

      return NextResponse.json(updatedSOW);

    } catch (error) {
      return NextResponse.json(
        { error: 'SOW not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('SOW update error:', error);
    return NextResponse.json(
      { error: 'Failed to update SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// DELETE - Delete a specific SOW
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const resolvedParams = await params;
  try {
    const sowId = resolvedParams.id;

    if (!sowId) {
      return NextResponse.json(
        { error: 'SOW ID is required' },
        { status: 400 }
      );
    }

    const uploadsDir = join(process.cwd(), 'uploads');
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);
    const sowsListPath = join(uploadsDir, 'sows.json');

    // Remove from SOWs list
    try {
      const sowsData = await readFile(sowsListPath, 'utf-8');
      let sowsList: SavedSOW[] = JSON.parse(sowsData);

      const originalLength = sowsList.length;
      sowsList = sowsList.filter(sow => sow.id !== sowId);

      if (sowsList.length === originalLength) {
        return NextResponse.json(
          { error: 'SOW not found in list' },
          { status: 404 }
        );
      }

      // Save updated list
      await writeFile(sowsListPath, JSON.stringify(sowsList, null, 2));

      // Delete individual SOW file
      try {
        await unlink(sowFilePath);
      } catch (error) {
        console.warn('SOW file not found, but removed from list:', error);
      }

      console.log('SOW deleted successfully:', sowId);

      return NextResponse.json({
        message: 'SOW deleted successfully'
      });

    } catch (error) {
      return NextResponse.json(
        { error: 'SOWs list not found' },
        { status: 404 }
      );
    }

  } catch (error) {
    console.error('SOW deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}