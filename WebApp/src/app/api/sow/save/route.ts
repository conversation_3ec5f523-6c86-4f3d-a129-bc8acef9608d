import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { v4 as uuidv4 } from 'uuid';

interface SavedSOW {
  id: string;
  title: string;
  clientName: string;
  projectName: string;
  templateName: string;
  templateId?: string;
  markdown: string;
  createdAt: string;
  status: 'draft' | 'final';
  fileSize?: number;
}

// POST - Save a generated SOW
export async function POST(request: NextRequest) {
  try {
    const {
      title,
      clientName,
      projectName,
      templateName,
      templateId,
      markdown,
      status = 'final'
    } = await request.json();

    if (!title || !markdown) {
      return NextResponse.json(
        { error: 'Title and markdown content are required' },
        { status: 400 }
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'uploads');
    try {
      await mkdir(uploadsDir, { recursive: true });
    } catch (error) {
      // Directory might already exist
    }

    // Generate unique ID for the SOW
    const sowId = uuidv4();
    
    // Create SOW object
    const sowData: SavedSOW = {
      id: sowId,
      title,
      clientName: clientName || 'Unknown Client',
      projectName: projectName || 'Untitled Project',
      templateName: templateName || 'Unknown Template',
      templateId: templateId || undefined,
      markdown,
      createdAt: new Date().toISOString(),
      status,
      fileSize: markdown.length
    };

    // Save individual SOW file
    const sowFilePath = join(uploadsDir, `sow-${sowId}.json`);
    await writeFile(sowFilePath, JSON.stringify(sowData, null, 2));

    // Update SOWs list
    const sowsListPath = join(uploadsDir, 'sows.json');
    let sowsList: SavedSOW[] = [];
    
    try {
      const existingData = await readFile(sowsListPath, 'utf-8');
      sowsList = JSON.parse(existingData);
    } catch (error) {
      // File doesn't exist, start with empty array
    }

    // Add new SOW to list (keep only essential info for listing)
    const sowListItem: SavedSOW = {
      id: sowId,
      title,
      clientName: clientName || 'Unknown Client',
      projectName: projectName || 'Untitled Project',
      templateName: templateName || 'Unknown Template',
      markdown: '', // Don't store full markdown in list
      createdAt: sowData.createdAt,
      status,
      fileSize: markdown.length
    };

    sowsList.unshift(sowListItem); // Add to beginning of array

    // Keep only the most recent 100 SOWs
    if (sowsList.length > 100) {
      sowsList = sowsList.slice(0, 100);
    }

    // Save updated list
    await writeFile(sowsListPath, JSON.stringify(sowsList, null, 2));

    console.log('SOW saved successfully:', sowListItem);

    return NextResponse.json({
      message: 'SOW saved successfully',
      id: sowId,
      sow: sowListItem
    });

  } catch (error) {
    console.error('SOW save error:', error);
    return NextResponse.json(
      { error: 'Failed to save SOW: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}

// GET - Load saved SOWs list
export async function GET() {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const sowsListPath = join(uploadsDir, 'sows.json');

    try {
      const sowsData = await readFile(sowsListPath, 'utf-8');
      const sows: SavedSOW[] = JSON.parse(sowsData);
      
      // Sort by creation date (newest first)
      sows.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      
      return NextResponse.json(sows);
    } catch (error) {
      // Return empty array if file doesn't exist
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error('SOWs load error:', error);
    return NextResponse.json(
      { error: 'Failed to load SOWs' },
      { status: 500 }
    );
  }
}
