import { NextRequest, NextResponse } from 'next/server';
import { createSOW, getSOWs, updateSOW, deleteSOW } from '@/lib/database/sow';

export async function POST(request: NextRequest) {
  try {
    const sowData = await request.json();
    const newSOW = await createSOW(sowData);
    return NextResponse.json(newSOW, { status: 201 });
  } catch (error) {
    console.error('Error creating SOW:', error);
    return NextResponse.json(
      { message: 'Error creating SOW', error: String(error) }, 
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    const sows = await getSOWs();
    return NextResponse.json(sows, { status: 200 });
  } catch (error) {
    console.error('Error fetching SOWs:', error);
    return NextResponse.json(
      { message: 'Error fetching SOWs', error: String(error) }, 
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { id, ...updateData } = await request.json();
    const updatedSOW = await updateSOW(id, updateData);
    return NextResponse.json(updatedSOW, { status: 200 });
  } catch (error) {
    console.error('Error updating SOW:', error);
    return NextResponse.json(
      { message: 'Error updating SOW', error: String(error) }, 
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { id } = await request.json();
    await deleteSOW(id);
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting SOW:', error);
    return NextResponse.json(
      { message: 'Error deleting SOW', error: String(error) }, 
      { status: 500 }
    );
  }
}