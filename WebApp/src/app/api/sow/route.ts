import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sowData = await request.json();
    const newSOW = await DatabaseService.createSOW({
      ...sowData,
      userId
    });
    return NextResponse.json(newSOW, { status: 201 });
  } catch (error) {
    console.error('Error creating SOW:', error);
    return NextResponse.json(
      { message: 'Error creating SOW', error: String(error) },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const sows = await DatabaseService.getUserSOWs(userId);
    return NextResponse.json(sows, { status: 200 });
  } catch (error) {
    console.error('Error fetching SOWs:', error);
    return NextResponse.json(
      { message: 'Error fetching SOWs', error: String(error) },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id, ...updateData } = await request.json();
    const updatedSOW = await DatabaseService.updateSOW(id, updateData);
    return NextResponse.json(updatedSOW, { status: 200 });
  } catch (error) {
    console.error('Error updating SOW:', error);
    return NextResponse.json(
      { message: 'Error updating SOW', error: String(error) },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await request.json();
    await DatabaseService.deleteSOW(id, userId);
    return new NextResponse(null, { status: 204 });
  } catch (error) {
    console.error('Error deleting SOW:', error);
    return NextResponse.json(
      { message: 'Error deleting SOW', error: String(error) },
      { status: 500 }
    );
  }
}