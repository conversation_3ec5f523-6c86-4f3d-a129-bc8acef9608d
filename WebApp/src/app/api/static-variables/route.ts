import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET(request: NextRequest) {
  try {
    // Load settings for company information
    const uploadsDir = join(process.cwd(), 'uploads');
    const settingsPath = join(uploadsDir, 'settings.json');
    
    let settings;
    try {
      const settingsData = await readFile(settingsPath, 'utf-8');
      settings = JSON.parse(settingsData);
    } catch (error) {
      // Use default settings if file doesn't exist
      settings = getDefaultSettings();
    }

    // Generate comprehensive static variables
    const today = new Date();
    const nextMonth = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
    const nextQuarter = new Date(today.getTime() + 90 * 24 * 60 * 60 * 1000);

    const staticVariables = {
      // Date variables
      todaysDate: today.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      }),
      currentYear: today.getFullYear().toString(),
      currentMonth: today.toLocaleDateString('en-US', { month: 'long' }),
      currentDay: today.getDate().toString(),
      
      // Default date ranges
      defaultStartDate: today.toISOString().split('T')[0],
      defaultEndDate: nextMonth.toISOString().split('T')[0],
      defaultQuarterEndDate: nextQuarter.toISOString().split('T')[0],
      
      // Timeline options
      timelineOptions: [
        '1-2 weeks',
        '3-4 weeks', 
        '1-2 months',
        '2-3 months',
        '3-6 months',
        '6+ months'
      ],
      
      // Common project types
      commonProjectTypes: [
        'Web Application Development',
        'Mobile App Development',
        'Website Design & Development',
        'E-commerce Platform',
        'API Development & Integration',
        'Database Design & Implementation',
        'UI/UX Design',
        'Software Consulting',
        'System Integration',
        'Custom Software Development'
      ],
      
      // Common deliverables
      commonDeliverables: [
        'Fully functional web application',
        'Mobile application (iOS/Android)',
        'Responsive website design',
        'API documentation',
        'User training materials',
        'Technical documentation',
        'Source code and deployment guide',
        'Testing and QA reports',
        'Performance optimization',
        'Security audit and recommendations'
      ],
      
      // Payment schedule options
      paymentScheduleOptions: [
        '50% upfront, 50% on completion',
        '25% upfront, 25% at midpoint, 50% on completion',
        '33% upfront, 33% at midpoint, 34% on completion',
        '100% upfront',
        'Monthly billing',
        'Milestone-based payments'
      ],
      
      // Standard legal clauses
      standardClauses: {
        intellectualProperty: 'All custom work and intellectual property will transfer to client upon full payment.',
        confidentiality: 'Both parties agree to maintain confidentiality of proprietary information.',
        terminationClause: 'Either party may terminate with 30 days written notice. Payment due for work completed.',
        liabilityClause: 'Liability is limited to the total contract value.',
        disputeResolution: 'Disputes will be resolved through binding arbitration.',
        governingLaw: 'This agreement is governed by the laws of the state where services are performed.'
      }
    };

    // Combine with company settings
    const response = {
      staticVariables,
      companyInfo: settings.companyInfo || getDefaultSettings().companyInfo,
      projectDefaults: {
        ...settings.projectDefaults,
        defaultHourlyRate: settings.projectDefaults?.defaultHourlyRate || '150',
        defaultTimeline: settings.projectDefaults?.defaultTimeline || '8-12 weeks',
        defaultProjectType: settings.projectDefaults?.defaultProjectType || 'Web Application Development',
        defaultPaymentTerms: settings.projectDefaults?.defaultPaymentTerms || '50% upfront, 50% on completion',
        defaultBudgetRange: settings.projectDefaults?.defaultBudgetRange || '$10,000 - $25,000',
        
        // Standard milestones
        standardMilestones: [
          'Project kickoff and requirements gathering',
          'Design mockups and approval',
          'Development phase completion',
          'Testing and quality assurance',
          'Client review and feedback',
          'Final delivery and deployment'
        ]
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Failed to load static variables:', error);
    
    // Return fallback data
    return NextResponse.json({
      staticVariables: getDefaultStaticVariables(),
      companyInfo: getDefaultSettings().companyInfo,
      projectDefaults: getDefaultSettings().projectDefaults
    });
  }
}

function getDefaultSettings() {
  return {
    companyInfo: {
      companyName: 'QuantumRhino',
      contactName: 'Chase Vazquez',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Innovation Drive, Tech City, TC 12345',
      website: 'https://quantumrhino.com'
    },
    projectDefaults: {
      defaultProjectType: 'Web Application Development',
      defaultHourlyRate: '150',
      defaultTimeline: '8-12 weeks',
      defaultPaymentTerms: '50% upfront, 50% on completion',
      defaultBudgetRange: '$10,000 - $25,000'
    }
  };
}

function getDefaultStaticVariables() {
  const today = new Date();
  const nextMonth = new Date(today.getTime() + 30 * 24 * 60 * 60 * 1000);
  
  return {
    todaysDate: today.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long', 
      day: 'numeric' 
    }),
    currentYear: today.getFullYear().toString(),
    defaultStartDate: today.toISOString().split('T')[0],
    defaultEndDate: nextMonth.toISOString().split('T')[0],
    timelineOptions: ['1-2 weeks', '3-4 weeks', '1-2 months', '2-3 months'],
    commonProjectTypes: ['Web Development', 'Mobile Development', 'Consulting'],
    commonDeliverables: ['Web Application', 'Documentation', 'Training'],
    paymentScheduleOptions: ['50% upfront, 50% on completion', 'Monthly billing']
  };
}
