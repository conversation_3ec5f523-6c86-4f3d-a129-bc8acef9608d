import { NextRequest, NextResponse } from 'next/server';
import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';

interface TemplateListItem {
  id: string;
  name: string;
  uploadDate: string;
  fields: string[];
  fileSize: number;
}

// GET - Load templates list
export async function GET() {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const templatesListPath = join(uploadsDir, 'templates.json');

    try {
      const templatesData = await readFile(templatesListPath, 'utf-8');
      const templates: TemplateListItem[] = JSON.parse(templatesData);

      // Sort by upload date (newest first)
      templates.sort((a, b) => new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime());

      return NextResponse.json(templates);
    } catch (error) {
      // Return empty array if file doesn't exist
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error('Templates load error:', error);
    return NextResponse.json(
      { error: 'Failed to load templates' },
      { status: 500 }
    );
  }
}

// DELETE - Remove a template
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const templateId = searchParams.get('id');

    if (!templateId) {
      return NextResponse.json(
        { error: 'Template ID is required' },
        { status: 400 }
      );
    }

    const uploadsDir = join(process.cwd(), 'uploads');
    const templatesListPath = join(uploadsDir, 'templates.json');

    // Load current templates list
    let templatesList: TemplateListItem[] = [];
    try {
      const templatesData = await readFile(templatesListPath, 'utf-8');
      templatesList = JSON.parse(templatesData);
    } catch (error) {
      return NextResponse.json(
        { error: 'Templates list not found' },
        { status: 404 }
      );
    }

    // Remove template from list
    const updatedList = templatesList.filter(template => template.id !== templateId);

    if (updatedList.length === templatesList.length) {
      return NextResponse.json(
        { error: 'Template not found' },
        { status: 404 }
      );
    }

    // Save updated list
    await writeFile(templatesListPath, JSON.stringify(updatedList, null, 2));

    return NextResponse.json({
      message: 'Template removed successfully'
    });

  } catch (error) {
    console.error('Template deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete template: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}