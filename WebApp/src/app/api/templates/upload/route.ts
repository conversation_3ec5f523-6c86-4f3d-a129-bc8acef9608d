import { NextRequest, NextResponse } from 'next/server';
import { FileStorage } from '@/lib/storage';
import { DatabaseService } from '@/lib/database';
import { getMimeType } from '@/lib/storage';

export async function POST(request: NextRequest) {
  try {
    // Get user ID from headers (you'll need to implement auth middleware)
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const name = formData.get('name') as string;
    const description = formData.get('description') as string;
    const isDefault = formData.get('isDefault') === 'true';

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type (only allow .docx files)
    if (!file.name.endsWith('.docx')) {
      return NextResponse.json({ 
        error: 'Only .docx files are allowed' 
      }, { status: 400 });
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer());
    
    // Store file
    const fileInfo = await FileStorage.storeTemplate(
      userId,
      buffer,
      file.name,
      file.type
    );

    // Save to database
    const template = await DatabaseService.createTemplate({
      name: name || file.name,
      description,
      filePath: fileInfo.filePath,
      fileName: fileInfo.fileName,
      fileSize: fileInfo.fileSize,
      mimeType: getMimeType(file.name),
      userId,
      isDefault
    });

    return NextResponse.json({
      success: true,
      template: {
        id: template.id,
        name: template.name,
        description: template.description,
        fileName: template.fileName,
        fileSize: template.fileSize,
        isDefault: template.isDefault,
        createdAt: template.createdAt
      }
    });

  } catch (error) {
    console.error('Template upload error:', error);
    return NextResponse.json(
      { error: 'Failed to upload template' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const templates = await DatabaseService.getUserTemplates(userId);

    return NextResponse.json({
      success: true,
      templates: templates.map(template => ({
        id: template.id,
        name: template.name,
        description: template.description,
        fileName: template.fileName,
        fileSize: template.fileSize,
        isDefault: template.isDefault,
        isActive: template.isActive,
        createdAt: template.createdAt,
        updatedAt: template.updatedAt
      }))
    });

  } catch (error) {
    console.error('Get templates error:', error);
    return NextResponse.json(
      { error: 'Failed to get templates' },
      { status: 500 }
    );
  }
}
