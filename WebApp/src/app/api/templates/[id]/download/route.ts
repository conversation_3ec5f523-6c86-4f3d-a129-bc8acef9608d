import { NextRequest, NextResponse } from 'next/server';
import { FileStorage } from '@/lib/storage';
import { DatabaseService } from '@/lib/database';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const templateId = params.id;

    // Get template from database
    const template = await DatabaseService.getTemplate(templateId, userId);
    if (!template) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

    // Get file from storage
    const fileBuffer = await FileStorage.getFile(template.filePath);

    // Return file with proper headers
    return new NextResponse(fileBuffer, {
      headers: {
        'Content-Type': template.mimeType,
        'Content-Disposition': `attachment; filename="${template.fileName}"`,
        'Content-Length': template.fileSize.toString()
      }
    });

  } catch (error) {
    console.error('Template download error:', error);
    return NextResponse.json(
      { error: 'Failed to download template' },
      { status: 500 }
    );
  }
}
