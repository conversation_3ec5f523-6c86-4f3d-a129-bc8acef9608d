import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const { markdown, prompt, templateId } = await request.json();

    if (!markdown || !prompt) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log('🔄 PREVIEW: Starting live SOW preview generation...');
    console.log('Template ID:', templateId);
    console.log('Prompt length:', prompt.length);

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Live preview prompt - focused on generating a brief, updated preview
    const previewPrompt = `
TASK: Generate a brief, updated SOW preview based on the template and current information.

TEMPLATE:
${markdown}

CURRENT INFORMATION:
${prompt}

INSTRUCTIONS:
1. Create a concise SOW preview (2-3 paragraphs max) that incorporates the available information
2. Fill in the template with the provided details
3. Keep it brief but professional - this is just a preview
4. Focus on the key elements: client, project, timeline, budget, scope
5. If information is missing, use placeholder text like "[To be determined]"

RESPOND WITH ONLY THE PREVIEW TEXT (no JSON, no formatting):
`;

    console.log('🔄 PREVIEW: Sending preview request to Gemini...');
    
    const result = await model.generateContent(previewPrompt);
    const previewText = result.response.text();
    
    console.log('🔄 PREVIEW: Generated preview length:', previewText.length);
    console.log('🔄 PREVIEW: First 200 chars:', previewText.substring(0, 200));

    return NextResponse.json({
      success: true,
      preview: previewText.trim()
    });

  } catch (error) {
    console.error('🔄 PREVIEW: Error during preview generation:', error);
    
    return NextResponse.json({
      error: 'Preview generation failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
