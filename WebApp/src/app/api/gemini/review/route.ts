import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const { processedMarkdown, originalTemplate, userPrompt, templateId } = await request.json();

    if (!processedMarkdown || !originalTemplate || !userPrompt) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log('🔍 REVIEW: Starting post-processing missing info detection...');
    console.log('Template ID:', templateId);
    console.log('User prompt length:', userPrompt.length);
    console.log('Processed markdown length:', processedMarkdown.length);

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Post-processing review prompt to identify what's still missing after Gemini processing
    const reviewPrompt = `
TASK: Analyze the processed document to identify missing or incomplete information that needs user input.

**CONTEXT:**
You are reviewing a document that has already been processed by AI. Your job is to identify what information is still missing, incomplete, or needs clarification from the user.

**ORIGINAL TEMPLATE STRUCTURE:**
${originalTemplate}

**USER PROVIDED INFORMATION:**
${userPrompt}

**PROCESSED DOCUMENT (after AI processing):**
${processedMarkdown}

**ANALYSIS INSTRUCTIONS:**
1. Compare the processed document against the original template structure
2. Identify sections that are still incomplete, have placeholder text, or contain generic content
3. Determine what specific information the user needs to provide to complete these sections
4. Focus on critical business information that only the user can provide
5. Ignore minor formatting or stylistic issues

**RESPOND WITH JSON FORMAT:**
{
  "documentSummary": "Brief summary of what the processed document contains (2-3 sentences)",
  "missingInfo": [
    {
      "id": "specific_project_details",
      "title": "Specific Project Requirements",
      "description": "The document needs more detailed project specifications",
      "priority": "high",
      "category": "Project Details",
      "examples": "Technical specifications, feature requirements, acceptance criteria",
      "currentState": "Generic project description present",
      "needed": "Specific technical and functional requirements"
    },
    {
      "id": "project_scope_details",
      "title": "Detailed Project Scope",
      "description": "Comprehensive project description to fill scope sections in template",
      "priority": "high",
      "category": "Project Details",
      "examples": "Specific features, deliverables, technical requirements, project boundaries",
      "templateRequirement": "Template has project scope sections requiring detailed user input"
    }
  ],
  "completionScore": 45,
  "recommendations": [
    "Focus on client information first - multiple template placeholders depend on this",
    "Define project scope clearly as it drives most other template sections"
  ],
  "userDataAnalysis": {
    "providedFields": ["field1", "field2"],
    "missingFields": ["field3", "field4"],
    "templatePlaceholders": ["[CLIENT_NAME]", "{PROJECT_NAME}"]
  }
}

**KEY PRINCIPLES:**
- User input is the ONLY real data - everything else in template is placeholder
- Missing info = Template structural requirements NOT covered by user input
- Initial content = Preview using ONLY user-provided data
- Ignore all existing template text content completely
`;

    console.log('🔍 REVIEW: Sending review prompt to Gemini...');
    
    const result = await model.generateContent(reviewPrompt);
    const responseText = result.response.text();
    
    console.log('🔍 REVIEW: Raw Gemini response length:', responseText.length);
    console.log('🔍 REVIEW: First 500 chars:', responseText.substring(0, 500));

    // Parse JSON response
    let reviewData;
    try {
      // Clean the response to extract JSON
      const jsonMatch = responseText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        reviewData = JSON.parse(jsonMatch[0]);
      } else {
        throw new Error('No JSON found in response');
      }
    } catch (parseError) {
      console.error('🔍 REVIEW: JSON parsing failed:', parseError);
      console.log('🔍 REVIEW: Full response:', responseText);
      
      // Fallback: create structured response from text
      reviewData = {
        initialContent: "Initial SOW content generated based on provided information. Additional details needed for completion.",
        missingInfo: [
          {
            id: "project_scope",
            title: "Detailed Project Scope",
            description: "Comprehensive breakdown of all work to be performed, specific deliverables, and project boundaries",
            priority: "high",
            category: "Scope",
            examples: "Feature specifications, technical requirements, design mockups"
          },
          {
            id: "timeline_milestones",
            title: "Timeline & Milestones",
            description: "Specific project phases, key dates, and milestone deliverables with deadlines",
            priority: "high",
            category: "Timeline",
            examples: "Phase 1: Discovery (Week 1-2), Phase 2: Development (Week 3-8)"
          },
          {
            id: "payment_schedule",
            title: "Payment Terms",
            description: "Payment amounts, schedule, and terms including late fees and cancellation policies",
            priority: "medium",
            category: "Financial",
            examples: "50% upfront, 25% at midpoint, 25% on completion, Net 30 terms"
          },
          {
            id: "technical_specs",
            title: "Technical Requirements",
            description: "Specific technologies, platforms, integrations, and technical constraints",
            priority: "medium",
            category: "Technical",
            examples: "React.js, Node.js, AWS hosting, third-party API integrations"
          }
        ],
        completionScore: 60,
        recommendations: [
          "Add more specific project details",
          "Include clear deliverables and timelines"
        ]
      };
    }

    console.log('🔍 REVIEW: Parsed review data:', reviewData);
    console.log('🔍 REVIEW: Missing info count:', reviewData.missingInfo?.length || 0);
    console.log('🔍 REVIEW: Completion score:', reviewData.completionScore);

    return NextResponse.json({
      success: true,
      initialContent: reviewData.initialContent || '',
      missingInfo: reviewData.missingInfo || [],
      completionScore: reviewData.completionScore || 70,
      recommendations: reviewData.recommendations || []
    });

  } catch (error) {
    console.error('🔍 REVIEW: Error during review process:', error);
    
    return NextResponse.json({
      error: 'Review failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
