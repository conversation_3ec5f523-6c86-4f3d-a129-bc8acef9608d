import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { readFile, writeFile } from 'fs/promises';
import { join } from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Initialize Gemini AI
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY || '');

// Load company settings
async function loadCompanySettings() {
  try {
    const uploadsDir = join(process.cwd(), 'uploads');
    const settingsPath = join(uploadsDir, 'settings.json');
    const settingsData = await readFile(settingsPath, 'utf-8');
    const settings = JSON.parse(settingsData);
    return settings.companyInfo;
  } catch (error) {
    // Return default settings if file doesn't exist
    return {
      companyName: 'QuantumRhino',
      contactName: '<PERSON>quez',
      email: '<EMAIL>',
      phone: '+****************',
      address: '123 Innovation Drive, Tech City, TC 12345',
      website: 'https://quantumrhino.com'
    };
  }
}

// Validate structure match between original and generated content
function validateStructureMatch(original: string, generated: string): { isMatch: boolean; issues: string[] } {
  const issues: string[] = [];

  // Extract headers from both
  const originalHeaders = original.match(/^#{1,6}\s+.+$/gm) || [];
  const generatedHeaders = generated.match(/^#{1,6}\s+.+$/gm) || [];

  // Only check for major header discrepancies (more than 30% difference)
  const headerCountDiff = Math.abs(originalHeaders.length - generatedHeaders.length);
  const headerCountThreshold = Math.max(3, originalHeaders.length * 0.3);

  if (headerCountDiff > headerCountThreshold) {
    issues.push(`Significant header count mismatch: original has ${originalHeaders.length}, generated has ${generatedHeaders.length}`);
  }

  // Extract lists from both (simplified validation)
  const originalLists = original.match(/^[\s]*[-*+]\s+.+$/gm) || [];
  const generatedLists = generated.match(/^[\s]*[-*+]\s+.+$/gm) || [];

  // Only check if there's a major discrepancy (more than 50% difference)
  const listCountDiff = Math.abs(originalLists.length - generatedLists.length);
  const listCountThreshold = Math.max(5, originalLists.length * 0.5);

  if (listCountDiff > listCountThreshold) {
    issues.push(`Significant list item count mismatch: original has ${originalLists.length}, generated has ${generatedLists.length}`);
  }

  return {
    isMatch: issues.length === 0,
    issues
  };
}

// Validate and preprocess user input
function validateAndPreprocessInput(prompt: string): { isValid: boolean; processedPrompt: string; warnings: string[] } {
  const warnings: string[] = [];
  let processedPrompt = prompt.trim();

  // Check for minimum information
  const hasClientInfo = /client|company|customer/i.test(processedPrompt);
  const hasProjectInfo = /project|work|task|service/i.test(processedPrompt);
  const hasBudgetInfo = /budget|cost|price|\$|dollar|fee/i.test(processedPrompt);
  const hasTimelineInfo = /timeline|deadline|date|week|month|schedule/i.test(processedPrompt);

  if (!hasClientInfo) {
    warnings.push('No client information detected - will use placeholders');
  }
  if (!hasProjectInfo) {
    warnings.push('No project details detected - will use generic descriptions');
  }
  if (!hasBudgetInfo) {
    warnings.push('No budget information detected - will use placeholder amounts');
  }
  if (!hasTimelineInfo) {
    warnings.push('No timeline information detected - will use placeholder dates');
  }

  // Enhance prompt with missing information guidance
  if (warnings.length > 0) {
    processedPrompt += '\n\nNOTE: Some information appears to be missing. Please use professional placeholders for any missing details and indicate what information needs to be provided later.';
  }

  return {
    isValid: true, // Always valid - just provide warnings
    processedPrompt,
    warnings
  };
}

// Create intelligent fallback when Gemini fails
function createIntelligentFallback(originalMarkdown: string, userPrompt: string, companyInfo: any): string {
  console.log('🔄 FALLBACK: Creating intelligent fallback content...');

  let fallbackContent = originalMarkdown;

  // Extract key information from user prompt
  const clientMatch = userPrompt.match(/client[:\s]+([^,.\n]+)/i);
  const projectMatch = userPrompt.match(/project[:\s]+([^,.\n]+)/i);
  const budgetMatch = userPrompt.match(/budget[:\s]*\$?([0-9,]+)/i);
  const timelineMatch = userPrompt.match(/timeline[:\s]+([^,.\n]+)/i);

  // Basic replacements
  const replacements = [
    // Company information
    { pattern: /\[COMPANY[_\s]*NAME\]/gi, value: companyInfo.companyName },
    { pattern: /\[CONTACT[_\s]*NAME\]/gi, value: companyInfo.contactName },
    { pattern: /\[EMAIL\]/gi, value: companyInfo.email },
    { pattern: /\[PHONE\]/gi, value: companyInfo.phone },
    { pattern: /\[ADDRESS\]/gi, value: companyInfo.address },
    { pattern: /\[WEBSITE\]/gi, value: companyInfo.website },

    // Client information
    { pattern: /\[CLIENT[_\s]*NAME\]/gi, value: clientMatch ? clientMatch[1].trim() : '[CLIENT NAME TO BE PROVIDED]' },
    { pattern: /\[PROJECT[_\s]*NAME\]/gi, value: projectMatch ? projectMatch[1].trim() : '[PROJECT NAME TO BE PROVIDED]' },
    { pattern: /\[BUDGET\]/gi, value: budgetMatch ? `$${budgetMatch[1]}` : '[BUDGET TO BE DETERMINED]' },
    { pattern: /\[TIMELINE\]/gi, value: timelineMatch ? timelineMatch[1].trim() : '[TIMELINE TO BE DETERMINED]' },

    // Dates
    { pattern: /\[START[_\s]*DATE\]/gi, value: '[START DATE TO BE DETERMINED]' },
    { pattern: /\[END[_\s]*DATE\]/gi, value: '[END DATE TO BE DETERMINED]' },
    { pattern: /\[DATE\]/gi, value: new Date().toLocaleDateString() },
  ];

  // Apply replacements
  replacements.forEach(({ pattern, value }) => {
    fallbackContent = fallbackContent.replace(pattern, value);
  });

  // Add fallback notice
  fallbackContent += '\n\n---\n*Note: This SOW was generated using fallback processing. Please review and update any placeholder information marked with brackets.*';

  console.log('✅ FALLBACK: Intelligent fallback content created');
  return fallbackContent;
}

// Validate template structure and content
async function validateTemplateStructure(markdown: string, templateId: string): Promise<{ isValid: boolean; issues: string[]; suggestions: string[] }> {
  const issues: string[] = [];
  const suggestions: string[] = [];

  // Check for basic SOW elements
  const hasHeaders = /^#{1,6}\s+/m.test(markdown);
  const hasSOWKeywords = /statement\s+of\s+work|scope|deliverable|timeline|budget|cost/i.test(markdown);
  const hasPlaceholders = /\[.*\]|\{.*\}|_____|XXXX/i.test(markdown);
  const hasContactInfo = /email|phone|address|contact/i.test(markdown);

  if (!hasHeaders) {
    issues.push('No clear document structure detected (missing headers)');
    suggestions.push('Will add standard SOW section headers');
  }

  if (!hasSOWKeywords) {
    issues.push('Document does not appear to be SOW-related');
    suggestions.push('Will adapt content to SOW format while preserving structure');
  }

  if (!hasPlaceholders) {
    issues.push('No placeholders detected for dynamic content');
    suggestions.push('Will identify areas that need client-specific information');
  }

  if (!hasContactInfo) {
    issues.push('No contact information fields detected');
    suggestions.push('Will add contact information sections');
  }

  // Check document length
  if (markdown.length < 500) {
    issues.push('Template appears to be very short');
    suggestions.push('Will enhance with standard SOW content');
  }

  if (markdown.length > 50000) {
    issues.push('Template is very large - may affect processing');
    suggestions.push('Will focus on key sections for updates');
  }

  return {
    isValid: true, // Always valid - just provide feedback
    issues,
    suggestions
  };
}

// Get original DOCX structure for comparison
async function getOriginalDocxStructure(templateId: string): Promise<string> {
  try {
    console.log('🔍 STRUCTURE EXTRACTION: Getting original DOCX structure...');

    const uploadsDir = join(process.cwd(), 'uploads');
    const originalDocxPath = join(uploadsDir, `${templateId}.docx`);

    // Convert original DOCX to markdown to get exact structure with content control preservation
    const tempStructureMd = join(uploadsDir, `structure-${templateId}-${Date.now()}.md`);
    const pandocCommand = `pandoc "${originalDocxPath}" -f docx -t markdown --wrap=none --preserve-tabs --extract-media=./temp-media -o "${tempStructureMd}"`;

    console.log('🔍 STRUCTURE EXTRACTION: Running pandoc command:', pandocCommand);
    await execAsync(pandocCommand);

    const originalStructure = await readFile(tempStructureMd, 'utf-8');
    console.log('✅ STRUCTURE EXTRACTION: Original structure extracted, length:', originalStructure.length);

    // Clean up temp file
    try {
      await execAsync(`rm "${tempStructureMd}"`);
      await execAsync(`rm -rf ./temp-media`); // Clean up extracted media
    } catch (cleanupError) {
      console.warn('Cleanup warning:', cleanupError);
    }

    return originalStructure;

  } catch (error) {
    console.error('❌ STRUCTURE EXTRACTION: Failed to extract original structure:', error);
    throw new Error('Failed to extract original DOCX structure');
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🎯 GEMINI SELF-VALIDATION: Starting enhanced processing...');

    const { markdown, prompt, templateId } = await request.json();
    console.log('Request data received - markdown length:', markdown?.length, 'prompt length:', prompt?.length, 'templateId:', templateId);

    if (!markdown || !prompt || !templateId) {
      console.log('Missing required data - markdown:', !!markdown, 'prompt:', !!prompt, 'templateId:', !!templateId);
      return NextResponse.json({ error: 'Missing markdown, prompt, or templateId' }, { status: 400 });
    }

    // Validate and preprocess user input (non-blocking)
    console.log('🔍 VALIDATION: Analyzing user input...');
    const inputValidation = validateAndPreprocessInput(prompt);

    if (inputValidation.warnings.length > 0) {
      console.log('⚠️ VALIDATION: Input warnings:', inputValidation.warnings);
    }

    // Use the processed prompt (but don't block on validation failures)
    const processedPrompt = inputValidation.processedPrompt;

    // Load company settings and original structure
    const companyInfo = await loadCompanySettings();
    console.log('Loaded company info:', companyInfo);

    // Get original DOCX structure for comparison
    const originalStructure = await getOriginalDocxStructure(templateId);
    console.log('✅ Original DOCX structure extracted for comparison');

    // Validate template structure (non-blocking)
    console.log('🔍 VALIDATION: Analyzing template structure...');
    const templateValidation = await validateTemplateStructure(markdown, templateId);
    if (templateValidation.issues.length > 0) {
      console.log('⚠️ VALIDATION: Template issues detected:', templateValidation.issues);
      console.log('💡 VALIDATION: Applying suggestions:', templateValidation.suggestions);
    }

    // Check if we're in development mode with mock key
    console.log('Gemini API Key present:', !!process.env.GEMINI_API_KEY);
    console.log('API Key starts with:', process.env.GEMINI_API_KEY?.substring(0, 10) + '...');

    if (!process.env.GEMINI_API_KEY || process.env.GEMINI_API_KEY === 'YOUR_ACTUAL_GEMINI_API_KEY_HERE') {
      // Mock response for development
      console.log('Using mock Gemini API response for development');

      return NextResponse.json({
        updatedMarkdown: `${markdown}\n\n<!-- Mock Mode: Prompt received: ${prompt.substring(0, 100)}... -->`,
        message: 'SOW updated successfully with prompt information (Mock Mode)'
      });
    }

    // Get the generative model
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Create ultra-aggressive content replacement prompt
    const geminiPrompt = `
You are an AGGRESSIVE DOCUMENT TRANSFORMER. Your job is to COMPLETELY CLEAR template content and replace with user's real project information OR professional placeholders when no data provided.

🚨 **CRITICAL TRANSFORMATION RULES:**

1. **ELIMINATE ALL TEMPLATE CONTENT:** Remove every trace of template project names, companies, examples
2. **AGGRESSIVE CONTENT CLEARING:** Clear template content completely - use placeholders if no user data provided
3. **PRESERVE FORMATTING ONLY:** Keep document structure but replace ALL content
4. **NO TEMPLATE REMNANTS:** Zero tolerance for template examples or placeholder content
5. **COMPLETE PROJECT TRANSFORMATION:** Make this document 100% about user's project OR clean placeholders
6. **🚨 CRITICAL: PRESERVE ALL TABLE PIPE SEPARATORS (|)** - Removing these breaks content control anchors and formatting

🎯 **AGGRESSIVE REPLACEMENT PROTOCOL:**

**TEMPLATE CONTENT TO COMPLETELY ELIMINATE:**
- "Azure Middleware Integration" → User's actual project name OR "[PROJECT NAME]"
- "Toll Brothers" → User's actual client company OR "[CLIENT COMPANY]"
- Any template project descriptions → User's project description OR "[PROJECT DESCRIPTION TO BE PROVIDED]"
- Template deliverables → User's actual deliverables OR "[DELIVERABLES TO BE DEFINED]"
- Template timelines → User's actual timeline OR "[TIMELINE TO BE DETERMINED]"
- Template costs → User's actual budget OR "[BUDGET TO BE DETERMINED]"
- Template contact details → User's actual contact information OR "[CLIENT CONTACT INFORMATION]"

**🚨 CONTENT CONTROL ANCHOR HANDLING:**
- When you see "Company: [existing content]" → Replace with "Company: [user's company name]"
- When you see "| **Company:** | Old Company Name |" → Replace with "| **Company:** | ${companyInfo.companyName} |"
- NEVER break the field label (like "Company:") - always keep it intact and replace only the content after it
- Respect table cell boundaries - content should flow naturally within existing cell structures

**🚨 CRITICAL TABLE FORMATTING RULES:**
- ALWAYS preserve table pipe separators (|) - they are ESSENTIAL for content control anchors
- NEVER merge table cells or remove cell boundaries
- Each table cell must remain separate with proper | separators
- Example: "| **Company:** | QuantumRhino | **Date:** | January 20, 2025 |"
- NOT: "**Company:**QuantumRhino**Date:**January 20, 2025" (this breaks content control anchors)
- Keep table row structure intact with proper dashes and spacing

🚨 **CRITICAL: WHEN USER DATA IS MISSING:**
- DO NOT keep template examples like "Azure Middleware Integration"
- DO NOT keep template company names like "Toll Brothers"
- REPLACE with professional placeholders like "[PROJECT NAME]", "[CLIENT COMPANY]"
- CLEAR all template-specific content completely
- Use generic professional language instead of template examples

**PLACEHOLDER REPLACEMENTS:**
- [CLIENT_NAME] → Real client name
- [CLIENT_COMPANY] → Real client company
- [PROJECT_NAME] → Real project name
- [CLIENT_EMAIL] → Real client email
- [START_DATE] → Real start date
- [END_DATE] → Real end date
- [TOTAL_COST] → Real budget amount
- [Date] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- [TODAY] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- [CURRENT_DATE] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- [SIGNATURE_DATE] → Today's date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
- Any vendor fields → Company information provided

**CONTENT TRANSFORMATION RULES:**
- Replace ALL template content with user's project information
- Keep formatting structure but change ALL content
- Remove any mention of template projects/companies
- Make document 100% specific to user's project
- Preserve table/signature formatting but update content

**🚨 ABSOLUTE FORMATTING PRESERVATION:**

1. **TEXT INTEGRITY:** Never break words mid-sentence or add random line breaks
2. **NO ADDITIONS:** Never add notes, comments, or explanations
3. **EXACT COPYING:** Copy formatting character-by-character from template
4. **TABLE PRESERVATION:** Keep ALL table structure identical (pipes, dashes, spacing)
5. **SIGNATURE SECTIONS:** Keep signature formatting exactly as shown, only replace placeholder names/data
6. **CONTENT CONTROL ANCHORS:** Respect all content control boundaries - when you see field labels like "Company:" ensure the replacement text starts immediately after the colon and anchor, never break in the middle of table names or formatting boundaries

**FORBIDDEN ACTIONS:**
❌ Adding "(Note: Replace...)" or any explanatory text
❌ Breaking words across lines like "Co mpany" or "Signat ure"
❌ Adding random underscores or formatting changes
❌ Changing table column/row counts
❌ Adding new sections or content not in template
❌ Modifying bold/italic formatting patterns
❌ Breaking content control anchors or table cell markers
❌ Cutting text in the middle of table field names or labels
❌ Disrupting the boundary between field labels (like "Company:") and their content
❌ REMOVING TABLE PIPE SEPARATORS (|) - this destroys content control anchors
❌ Merging table cells together without proper separators
❌ Converting "| **Company:** | Value |" to "**Company:**Value" (this breaks anchors)

🏢 **COMPANY INFORMATION** (Use for ALL vendor/provider/contractor fields):
- Company Name: ${companyInfo.companyName}
- Contact Name: ${companyInfo.contactName}
- Email: ${companyInfo.email}
- Phone: ${companyInfo.phone}
- Address: ${companyInfo.address}
- Website: ${companyInfo.website}

📋 **USER'S PROJECT INFORMATION** (Use this data to replace ALL placeholder content):
${processedPrompt}

🚨 **HANDLING MISSING PROJECT INFORMATION:**
If the user has not provided specific project details:
- STILL CLEAR all template content like "Azure Middleware Integration", "Toll Brothers"
- REPLACE with professional placeholders: "[PROJECT NAME]", "[CLIENT COMPANY]", "[PROJECT DESCRIPTION TO BE PROVIDED]"
- NEVER leave template examples in the document
- Use generic professional language instead of template-specific content
- Example: "Azure Middleware Integration" → "[PROJECT NAME]" (NOT kept as template example)

📝 **TEMPLATE TO UPDATE** (Replace ALL content with real data above):
${markdown}

🎯 **AGGRESSIVE TRANSFORMATION STEPS:**

1. **SCAN FOR TEMPLATE CONTENT:** Find ALL references to template projects, companies, examples

2. **ELIMINATE TEMPLATE REFERENCES (MANDATORY):**
   - "Azure Middleware Integration" → User's project name OR "[PROJECT NAME]" if not provided
   - "Toll Brothers" → User's client company OR "[CLIENT COMPANY]" if not provided
   - Template project descriptions → User's project details OR "[PROJECT DESCRIPTION TO BE PROVIDED]"
   - Template deliverables → User's deliverables OR "[DELIVERABLES TO BE DEFINED]"
   - Template contact info → User's contact info OR "[CLIENT CONTACT INFORMATION]"

3. **REPLACE ALL PLACEHOLDERS:**
   - [CLIENT_NAME] → Real client name OR "[CLIENT NAME]" if not provided
   - [PROJECT_NAME] → Real project name OR "[PROJECT NAME]" if not provided
   - [CLIENT_COMPANY] → Real client company OR "[CLIENT COMPANY]" if not provided
   - [Date] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })} (always replace with current date)
   - [TODAY] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })} (always replace with current date)
   - [CURRENT_DATE] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })} (always replace with current date)
   - [SIGNATURE_DATE] → ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })} (always replace with current date)
   - All other placeholders → Real user data OR professional placeholder

4. **TRANSFORM PROJECT SECTIONS (CLEAR TEMPLATE CONTENT):**
   - Project description sections → User's actual project description OR "[PROJECT DESCRIPTION TO BE PROVIDED]"
   - Deliverables sections → User's actual deliverables OR "[DELIVERABLES TO BE DEFINED]"
   - Timeline sections → User's actual timeline OR "[TIMELINE TO BE DETERMINED]"
   - Cost sections → User's actual budget OR "[BUDGET TO BE DETERMINED]"

5. **MANDATORY CONTENT CLEARING:**
   - NEVER leave template project names like "Azure Middleware Integration"
   - NEVER leave template company names like "Toll Brothers"
   - ALWAYS clear template-specific content even if no user data provided
   - Use professional placeholders instead of template examples

5. **PRESERVE FORMATTING ONLY:**
   - Keep ALL bold formatting (**text**)
   - Keep ALL spacing and line breaks
   - Keep ALL table structures (pipes, dashes, alignment)
   - Keep ALL signature formatting
   - But replace ALL content with user's project information
   - **CRITICAL:** When you see field labels like "Company:" ensure the replacement text starts immediately after the colon and any content control anchor - never break in the middle of table names or field labels

6. **SPECIAL ATTENTION TO SIGNATURE TABLES AND FINAL SECTIONS:**
   - In signature tables, ALWAYS fill in today's date: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
   - Replace any date placeholders in final tables with actual current date
   - Look for "Date:" fields, "Signed on:" fields, or similar date entries in tables
   - Ensure signature sections have proper dates filled in, not left as placeholders
   - Common patterns to replace: "Date: ___", "Date: [DATE]", "Date: _______", "Signed: ___"

7. **FINAL VALIDATION:**
   - Zero template project references remain
   - Document is 100% about user's project
   - All formatting preserved perfectly
   - No broken words or added notes
   - All date placeholders filled with current date: ${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })}
   - Signature tables have proper dates, not blank lines or placeholders

**🚨 CRITICAL SUCCESS CRITERIA - MUST PASS ALL:**

✅ **ZERO TEMPLATE CONTENT:** No references to "Azure Middleware", "Toll Brothers", or other template projects
✅ **TEMPLATE CONTENT CLEARED:** All template-specific content replaced with user data OR professional placeholders
✅ **ALL PLACEHOLDERS HANDLED:** Every [PLACEHOLDER] replaced with real user data OR clean placeholder
✅ **CONTENT TRANSFORMATION:** Template project descriptions completely cleared and replaced
✅ **NO BROKEN WORDS:** Every word must be complete (never "Co mpany" or "Signat ure")
✅ **NO ADDED NOTES:** Zero explanatory notes like "(Note: Replace...)"
✅ **EXACT FORMATTING:** All bold, italic, spacing identical to template structure
✅ **PERFECT TABLES:** All table structure preserved with proper pipe separators (|) and cell boundaries intact
✅ **DATES PROPERLY FILLED:** All date fields in signature tables and final sections filled with current date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})
✅ **NO BLANK DATE FIELDS:** No "Date: ___" or "Date: [DATE]" placeholders left unfilled

**FINAL VALIDATION:**
Before returning, check for these CRITICAL ERRORS:
❌ Any template project names or companies remaining (like "Azure Middleware", "Toll Brothers")
❌ Template project descriptions not cleared/replaced
❌ Template-specific content still showing instead of user data or clean placeholders
❌ Broken words across lines
❌ Added explanatory notes or comments
❌ Changed table/signature formatting structure
❌ Removed table pipe separators (|) causing content control anchor breakage
❌ Merged table cells without proper boundaries
❌ Date placeholders left unfilled (Date: ___, Date: [DATE], Signed: ___, etc.)
❌ Signature tables missing current date (${new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' })})

**🚨 MANDATORY TEMPLATE CLEARING:**
- If user provides project info → Use their actual project information
- If user provides NO project info → Use clean professional placeholders like "[PROJECT NAME]"
- NEVER EVER keep template examples like "Azure Middleware Integration" or "Toll Brothers"
- ALWAYS clear template content regardless of whether user provided replacement data

Return ONLY the updated document with placeholders replaced and formatting preserved exactly:
`;

    // Generate content
    console.log('Calling Gemini API with prompt length:', geminiPrompt.length);

    try {
      // Step 1: Generate initial content
      console.log('🤖 GEMINI: Calling API with enhanced formatting validation...');
      const result = await model.generateContent(geminiPrompt);
      console.log('✅ GEMINI: API call successful');

      const response = await result.response;
      let initialContent = response.text().trim();

      console.log('📊 GEMINI: Initial content generated, length:', initialContent.length);

      // Step 2: Self-validation comparison
      console.log('🔍 GEMINI: Starting self-validation comparison...');

      const validationPrompt = `
You are a SILENT DOCUMENT CLEANER. Return ONLY the clean document with no additional text.

DOCUMENT TO CLEAN:
${initialContent}

INSTRUCTIONS:
- Fix any broken words or formatting issues
- Remove any comparison notes or explanations
- 🚨 CRITICAL: Fix any broken table formatting - ensure all table cells have proper pipe separators (|)
- If you see merged table content like "**Company:**Value**Date:**Value", fix it to "| **Company:** | Value | **Date:** | Value |"
- Return ONLY the document content
- NO additional text, notes, or comments
- NO "The document..." statements
- SILENT operation only

CLEAN DOCUMENT:`;

      // Step 3: Get validated/corrected content
      const validationResult = await model.generateContent(validationPrompt);
      const validationResponse = await validationResult.response;
      let finalContent = validationResponse.text().trim();

      // Additional cleanup: Remove any comparison notes that might have slipped through
      finalContent = finalContent
        .replace(/^The generated document has.*$/gm, '')
        .replace(/^Otherwise, the formatting is.*$/gm, '')
        .replace(/^The generated document is returned unchanged\.$/gm, '')
        .replace(/^In the .* section, the original template.*$/gm, '')
        .replace(/^This is a content change, not a formatting issue.*$/gm, '')
        .replace(/^\s*$/gm, '') // Remove empty lines created by removals
        .trim();

      console.log('📊 GEMINI: Final validated content length:', finalContent.length);
      console.log('✅ GEMINI: Self-validation and cleanup complete');

      // Structure comparison for logging
      console.log('🔍 VALIDATION: Performing structure comparison...');
      const structureValidation = validateStructureMatch(originalStructure, finalContent);

      if (!structureValidation.isMatch) {
        console.warn('⚠️ VALIDATION: Minor structure differences detected:', structureValidation.issues);
        console.log('✅ VALIDATION: Proceeding with validated content');
      } else {
        console.log('✅ VALIDATION: Perfect structure match achieved');
      }

      return NextResponse.json({
        updatedMarkdown: finalContent,
        message: 'SOW updated with self-validated formatting preservation',
        validation: {
          ...structureValidation,
          selfValidated: true
        }
      });
    } catch (geminiError) {
      console.error('Gemini API specific error:', geminiError);

      // Fallback to mock response if Gemini fails
      console.log('Falling back to mock response due to Gemini error');
      return NextResponse.json({
        updatedMarkdown: `${markdown}\n\n<!-- Fallback Mode: Gemini API failed, prompt received: ${prompt.substring(0, 100)}... -->`,
        message: 'SOW updated with fallback processing (Gemini API unavailable)'
      });
    }

  } catch (error) {
    console.error('General API error:', error);
    return NextResponse.json(
      { error: 'Failed to process request: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
