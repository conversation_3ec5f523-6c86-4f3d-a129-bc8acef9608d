import { NextRequest, NextResponse } from 'next/server';
import { GoogleGenerativeAI } from '@google/generative-ai';

const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY!);

export async function POST(request: NextRequest) {
  try {
    const { processedMarkdown, originalTemplate, userPrompt, templateId } = await request.json();

    if (!processedMarkdown || !originalTemplate || !userPrompt) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    console.log('🔍 MISSING INFO: Starting post-processing analysis...');
    console.log('Template ID:', templateId);
    console.log('User prompt length:', userPrompt.length);
    console.log('Processed markdown length:', processedMarkdown.length);

    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });

    // Post-processing missing info detection prompt
    const missingInfoPrompt = `
TASK: Analyze the processed SOW document to identify <PERSON>LY truly missing or incomplete information that requires user input.

**CONTEXT:**
You are reviewing a Statement of Work that has been processed by AI. Be REASONABLE and PRACTICAL - only flag information that is genuinely missing or incomplete. DO NOT be overly picky about details that can be reasonably inferred or are already adequate.

**ORIGINAL TEMPLATE STRUCTURE:**
${originalTemplate}

**USER PROVIDED INFORMATION:**
${userPrompt}

**PROCESSED DOCUMENT (after AI processing):**
${processedMarkdown}

**ANALYSIS INSTRUCTIONS - BE REASONABLE:**
1. **Only flag TRULY missing info**: Don't flag information that can be reasonably inferred from what's provided
2. **Accept reasonable names**: "John Deer" is a complete name - don't require middle names or additional parts
3. **Accept adequate contact info**: If basic contact details are provided, don't demand excessive additional details
4. **Focus on CRITICAL gaps only**: Only flag information that would make the SOW incomplete or unprofessional
5. **Infer reasonable details**: Use common sense to fill gaps rather than flagging everything as missing

**BE PRACTICAL ABOUT:**
- Names: Accept provided names as complete (don't require titles, middle names, etc.)
- Contact info: Basic email, phone, address is sufficient
- Project scope: If user provided a description, work with it rather than demanding excessive detail
- Dates and costs: If provided, accept them as adequate
- Technical details: Only flag if completely missing, not if just high-level

**ONLY FLAG IF TRULY MISSING:**
- Completely missing client information
- No project scope or description at all
- Missing critical dates or budget information
- Incomplete legal/contractual sections that affect validity

**RESPOND WITH JSON FORMAT:**
{
  "documentSummary": "Brief summary of the current document state and what it contains",
  "completionScore": 85,
  "missingInfo": [
    {
      "id": "core_deliverable_clarity",
      "title": "Core Deliverable Specifications",
      "description": "Only flag if deliverables are completely unclear or missing",
      "priority": "critical",
      "category": "Core Deliverables",
      "examples": "Specific deliverable details that affect project success",
      "currentState": "User provided adequate project description",
      "needed": "Only if deliverables are truly unclear for project execution",
      "section": "Project Deliverables"
    }
  ],
  "recommendations": [
    "Focus on core deliverables that are essential for project success",
    "Consider additional details that could enhance client communication",
    "Acknowledge what user has provided is adequate for most purposes"
  ],
  "coreDeliverables": {
    "identified": ["List deliverables that are clear from user input"],
    "needsClarification": ["Only deliverables that truly need more detail for success"]
  },
  "strengths": [
    "User has provided adequate project information",
    "Core project elements are present and workable",
    "Document structure is professional and complete"
  ]
}

**IMPORTANT GUIDELINES - BE EXTREMELY LENIENT:**
- ONLY flag information that is absolutely critical and completely missing
- ACCEPT any reasonable information provided by the user as adequate
- DO NOT demand excessive detail - work with what's provided intelligently
- INFER and recommend additional information that could be helpful (not required)
- Focus on CORE deliverables and project essentials only
- If user provided basic project info, consider it 80-90% complete
- Aim for completion scores of 80-95% for documents with basic project information
- Distinguish between "missing critical info" vs "could be enhanced with more detail"
`;

    console.log('🔍 MISSING INFO: Sending analysis request to Gemini...');
    
    const result = await model.generateContent(missingInfoPrompt);
    const response = await result.response;
    const text = response.text();

    console.log('🔍 MISSING INFO: Raw Gemini response length:', text.length);
    console.log('🔍 MISSING INFO: First 500 chars:', text.substring(0, 500));

    // Parse JSON response
    let analysisData;
    try {
      // Extract JSON from response (handle potential markdown formatting)
      const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```/) || text.match(/\{[\s\S]*\}/);
      const jsonStr = jsonMatch ? (jsonMatch[1] || jsonMatch[0]) : text;
      analysisData = JSON.parse(jsonStr);
    } catch (parseError) {
      console.error('🔍 MISSING INFO: JSON parsing failed:', parseError);
      
      // Fallback response
      analysisData = {
        documentSummary: "Document has been processed but may need additional user input for completion.",
        completionScore: 75,
        missingInfo: [
          {
            id: "review_required",
            title: "Document Review Required",
            description: "Please review the processed document and provide any missing information",
            priority: "medium",
            category: "General",
            examples: "Review all sections for accuracy and completeness",
            currentState: "Document processed",
            needed: "User review and additional input as needed",
            section: "General Review"
          }
        ],
        recommendations: [
          "Review the processed document carefully",
          "Provide any missing business-specific information",
          "Verify all details are accurate and complete"
        ],
        strengths: [
          "Document structure is complete",
          "Basic information has been processed"
        ]
      };
    }

    console.log('🔍 MISSING INFO: Analysis complete');
    console.log('🔍 MISSING INFO: Missing info items:', analysisData.missingInfo?.length || 0);
    console.log('🔍 MISSING INFO: Completion score:', analysisData.completionScore);

    return NextResponse.json({
      success: true,
      documentSummary: analysisData.documentSummary || '',
      completionScore: analysisData.completionScore || 75,
      missingInfo: analysisData.missingInfo || [],
      recommendations: analysisData.recommendations || [],
      strengths: analysisData.strengths || []
    });

  } catch (error) {
    console.error('🔍 MISSING INFO: Error during analysis:', error);
    
    return NextResponse.json({
      error: 'Missing info analysis failed: ' + (error instanceof Error ? error.message : 'Unknown error'),
      details: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
