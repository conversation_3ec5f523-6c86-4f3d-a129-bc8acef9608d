import { NextRequest, NextResponse } from 'next/server';
import { generateSOWPDF } from '@/lib/pdf/generator';

export async function POST(request: NextRequest) {
  try {
    const { sowData } = await request.json();
    const pdfBuffer = await generateSOWPDF(sowData);
    
    const headers = new Headers();
    headers.set('Content-Type', 'application/pdf');
    headers.set('Content-Disposition', 'attachment; filename="sow.pdf"');
    
    return new NextResponse(pdfBuffer, {
      status: 200,
      headers,
    });
  } catch (error) {
    console.error('Error generating PDF:', error);
    return NextResponse.json(
      { error: 'Failed to generate PDF' }, 
      { status: 500 }
    );
  }
}