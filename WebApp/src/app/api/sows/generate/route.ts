import { NextRequest, NextResponse } from 'next/server';
import { FileStorage } from '@/lib/storage';
import { DatabaseService } from '@/lib/database';
import { Document, Packer, Paragraph, TextRun } from 'docx';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    const userId = request.headers.get('x-user-id');
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const {
      projectName,
      clientName,
      vendorName,
      scopeOfWork,
      timeline,
      pricing,
      templateId,
      companyId
    } = body;

    // Validate required fields
    if (!projectName || !clientName || !vendorName || !scopeOfWork) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create SOW in database first
    const sow = await DatabaseService.createSOW({
      projectName,
      clientName,
      vendorName,
      scopeOfWork,
      timeline,
      pricing,
      templateId,
      companyId,
      userId
    });

    // Generate document
    let docBuffer: Buffer;

    if (templateId) {
      // Use template-based generation
      const template = await DatabaseService.getTemplate(templateId, userId);
      if (!template) {
        return NextResponse.json({ error: 'Template not found' }, { status: 404 });
      }

      // Get template file
      const templateBuffer = await FileStorage.getFile(template.filePath);
      
      // TODO: Implement template processing with docx-templates
      // For now, create a basic document
      docBuffer = await generateBasicSOW({
        projectName,
        clientName,
        vendorName,
        scopeOfWork,
        timeline,
        pricing
      });
    } else {
      // Generate basic SOW document
      docBuffer = await generateBasicSOW({
        projectName,
        clientName,
        vendorName,
        scopeOfWork,
        timeline,
        pricing
      });
    }

    // Store generated file
    const fileName = `SOW_${projectName.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.docx`;
    const fileInfo = await FileStorage.storeSOW(userId, sow.id, docBuffer, fileName);

    // Update SOW with file information
    const updatedSOW = await DatabaseService.updateSOW(sow.id, {
      filePath: fileInfo.filePath,
      fileName,
      fileSize: fileInfo.fileSize
    });

    return NextResponse.json({
      success: true,
      sow: {
        id: updatedSOW.id,
        projectName: updatedSOW.projectName,
        clientName: updatedSOW.clientName,
        fileName: updatedSOW.fileName,
        fileSize: updatedSOW.fileSize,
        createdAt: updatedSOW.createdAt
      }
    });

  } catch (error) {
    console.error('SOW generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate SOW' },
      { status: 500 }
    );
  }
}

async function generateBasicSOW(data: {
  projectName: string;
  clientName: string;
  vendorName: string;
  scopeOfWork: string;
  timeline: string;
  pricing: string;
}): Promise<Buffer> {
  const doc = new Document({
    sections: [{
      properties: {},
      children: [
        new Paragraph({
          children: [
            new TextRun({
              text: "STATEMENT OF WORK",
              bold: true,
              size: 32
            })
          ]
        }),
        new Paragraph({ text: "" }),
        new Paragraph({
          children: [
            new TextRun({
              text: `Project: ${data.projectName}`,
              bold: true,
              size: 24
            })
          ]
        }),
        new Paragraph({ text: "" }),
        new Paragraph({
          children: [
            new TextRun({
              text: "Client Information:",
              bold: true
            })
          ]
        }),
        new Paragraph({
          text: `Client Name: ${data.clientName}`
        }),
        new Paragraph({ text: "" }),
        new Paragraph({
          children: [
            new TextRun({
              text: "Vendor Information:",
              bold: true
            })
          ]
        }),
        new Paragraph({
          text: `Vendor Name: ${data.vendorName}`
        }),
        new Paragraph({ text: "" }),
        new Paragraph({
          children: [
            new TextRun({
              text: "Scope of Work:",
              bold: true
            })
          ]
        }),
        new Paragraph({
          text: data.scopeOfWork
        }),
        new Paragraph({ text: "" }),
        new Paragraph({
          children: [
            new TextRun({
              text: "Timeline:",
              bold: true
            })
          ]
        }),
        new Paragraph({
          text: data.timeline
        }),
        new Paragraph({ text: "" }),
        new Paragraph({
          children: [
            new TextRun({
              text: "Pricing:",
              bold: true
            })
          ]
        }),
        new Paragraph({
          text: data.pricing
        })
      ]
    }]
  });

  return await Packer.toBuffer(doc);
}
