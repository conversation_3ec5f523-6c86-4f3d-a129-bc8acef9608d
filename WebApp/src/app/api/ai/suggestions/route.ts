import { NextRequest, NextResponse } from 'next/server';
import { openaiClient } from '@/lib/ai/openai-client';

export async function POST(request: NextRequest) {
  try {
    const { prompt } = await request.json();

    if (!prompt) {
      return NextResponse.json(
        { error: 'Prompt is required' }, 
        { status: 400 }
      );
    }

    const suggestions = await openaiClient.generateSuggestions(prompt);
    return NextResponse.json({ suggestions }, { status: 200 });
  } catch (error) {
    console.error('Error generating suggestions:', error);
    return NextResponse.json(
      { error: 'Failed to fetch suggestions' }, 
      { status: 500 }
    );
  }
}