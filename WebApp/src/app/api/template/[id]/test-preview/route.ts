import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

// Test endpoint to serve original DOCX for preview testing
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: templateId } = await params;
    console.log('TEST PREVIEW: Serving original DOCX for template:', templateId);

    const uploadsDir = join(process.cwd(), 'uploads');
    const docxPath = join(uploadsDir, `${templateId}.docx`);

    // Read the original DOCX file
    const docxBuffer = await readFile(docxPath);
    console.log('TEST PREVIEW: Original DOCX size:', docxBuffer.length);
    console.log('TEST PREVIEW: First 10 bytes:', docxBuffer.subarray(0, 10).toString('hex'));

    // Return the original DOCX file
    return new NextResponse(docxBuffer, {
      headers: {
        'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'Content-Disposition': `inline; filename="test-preview-${templateId}.docx"`,
        'Content-Length': docxBuffer.length.toString(),
        'Cache-Control': 'no-cache',
      },
    });

  } catch (error) {
    console.error('TEST PREVIEW error:', error);
    return NextResponse.json(
      { error: 'Failed to serve test preview: ' + (error instanceof Error ? error.message : 'Unknown error') },
      { status: 500 }
    );
  }
}
