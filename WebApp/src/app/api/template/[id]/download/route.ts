import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: templateId } = await params;
    const uploadsDir = join(process.cwd(), 'uploads');
    const docxPath = join(uploadsDir, `${templateId}.docx`);

    // Get template metadata for filename
    let filename = 'template.docx';
    try {
      const metadataPath = join(uploadsDir, `${templateId}.json`);
      const metadataContent = await readFile(metadataPath, 'utf-8');
      const metadata = JSON.parse(metadataContent);
      filename = metadata.name;
    } catch (error) {
      console.warn('Could not load template metadata, using default filename');
    }

    // Read and serve the original DOCX file exactly as uploaded
    try {
      const docxBuffer = await readFile(docxPath);
      
      console.log(`Serving original DOCX file: ${filename} (${docxBuffer.length} bytes)`);
      
      // Return the original DOCX file exactly as uploaded - no modifications
      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': `attachment; filename="${filename}"`,
          'Content-Length': docxBuffer.length.toString(),
          'Cache-Control': 'no-cache',
        },
      });
      
    } catch (error) {
      return NextResponse.json({ error: 'Template file not found' }, { status: 404 });
    }

  } catch (error) {
    console.error('DOCX download error:', error);
    return NextResponse.json({ error: 'Failed to download template' }, { status: 500 });
  }
}
