import { NextRequest, NextResponse } from 'next/server';
import { readFile } from 'fs/promises';
import { join } from 'path';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: templateId } = await params;
    const uploadsDir = join(process.cwd(), 'uploads');
    const docxPath = join(uploadsDir, `${templateId}.docx`);

    // Check if DOCX file exists and serve it directly
    try {
      const docxBuffer = await readFile(docxPath);

      // Return the original DOCX file for browser to handle
      return new NextResponse(docxBuffer, {
        headers: {
          'Content-Type': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'Content-Disposition': 'inline; filename="template-preview.docx"',
        },
      });

    } catch (error) {
      return NextResponse.json({ error: 'Template not found' }, { status: 404 });
    }

  } catch (error) {
    console.error('DOCX preview error:', error);
    return NextResponse.json({ error: 'Failed to serve DOCX file' }, { status: 500 });
  }
}
