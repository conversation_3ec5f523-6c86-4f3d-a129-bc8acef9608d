'use client';

import React from 'react';
import useSOW from '../../../hooks/use-sow';
import Button from '../../components/ui/button';

const SaveDraft: React.FC = () => {
    const { sowData, updateSOW } = useSOW();

    const handleSave = async () => {
        if (!sowData) {
            alert('No SOW data to save');
            return;
        }
        
        try {
            await updateSOW(sowData);
            alert('Draft saved successfully!');
        } catch (error) {
            console.error('Error saving draft:', error);
            alert('Failed to save draft. Please try again.');
        }
    };

    return (
        <div className="save-draft">
            <Button onClick={handleSave}>Save Draft</Button>
        </div>
    );
};

export default SaveDraft;