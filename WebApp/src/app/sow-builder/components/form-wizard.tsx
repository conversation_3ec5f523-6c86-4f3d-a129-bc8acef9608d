'use client';

import React, { useState, useCallback, useEffect } from 'react';
import CompanyInfoForm from '../../components/forms/company-info-form';
import ProjectDetailsForm from '../../components/forms/project-details-form';
import ScopeOfWorkForm from '../../components/forms/scope-of-work-form';
import TimelineForm from '../../components/forms/timeline-form';
import PricingForm from '../../components/forms/pricing-form';
import SaveDraft from './save-draft';

// Simple step indicator component
const StepIndicator = ({ currentStep, totalSteps }: { currentStep: number; totalSteps: number }) => (
    <div className="flex justify-center mb-8">
        {Array.from({ length: totalSteps }, (_, i) => (
            <div
                key={i}
                className={`w-8 h-8 rounded-full flex items-center justify-center mx-2 ${
                    i <= currentStep ? 'bg-blue-500 text-white' : 'bg-gray-300 text-gray-600'
                }`}
            >
                {i + 1}
            </div>
        ))}
    </div>
);

const FormWizard = () => {
    const [currentStep, setCurrentStep] = useState(0);
    const totalSteps = 5;
    const [formData, setFormData] = useState({
        companyInfo: {},
        projectDetails: {},
        scopeOfWork: {},
        timeline: {},
        pricing: {},
    });
    const [lastSaved, setLastSaved] = useState<Date | null>(null);
    const [isSaving, setIsSaving] = useState(false);

    // Auto-save functionality
    useEffect(() => {
        const autoSave = async () => {
            if (Object.keys(formData.companyInfo).length > 0 ||
                Object.keys(formData.projectDetails).length > 0) {
                setIsSaving(true);
                try {
                    // Simulate API call to save draft
                    await new Promise(resolve => setTimeout(resolve, 500));
                    localStorage.setItem('sowDraft', JSON.stringify({
                        formData,
                        currentStep,
                        savedAt: new Date().toISOString()
                    }));
                    setLastSaved(new Date());
                } catch (error) {
                    console.error('Auto-save failed:', error);
                } finally {
                    setIsSaving(false);
                }
            }
        };

        const timeoutId = setTimeout(autoSave, 2000); // Auto-save after 2 seconds of inactivity
        return () => clearTimeout(timeoutId);
    }, [formData, currentStep]);

    // Load saved draft on component mount
    useEffect(() => {
        const savedDraft = localStorage.getItem('sowDraft');
        if (savedDraft) {
            try {
                const { formData: savedFormData, currentStep: savedStep } = JSON.parse(savedDraft);
                setFormData(savedFormData);
                setCurrentStep(savedStep);
                setLastSaved(new Date());
            } catch (error) {
                console.error('Failed to load saved draft:', error);
            }
        }
    }, []);

    const handleNext = () => {
        if (currentStep < totalSteps - 1) {
            setCurrentStep(currentStep + 1);
        }
    };

    const handlePrev = () => {
        if (currentStep > 0) {
            setCurrentStep(currentStep - 1);
        }
    };

    const isLastStep = () => currentStep === totalSteps - 1;

    const handleCompanyInfoChange = useCallback((data: any) => {
        setFormData(prev => ({ ...prev, companyInfo: data }));
    }, []);

    const handleProjectDetailsChange = useCallback((data: any) => {
        setFormData(prev => ({ ...prev, projectDetails: data }));
    }, []);

    const handleScopeOfWorkChange = useCallback((data: any) => {
        setFormData(prev => ({ ...prev, scopeOfWork: data }));
    }, []);

    const handleTimelineChange = useCallback((data: any) => {
        setFormData(prev => ({ ...prev, timeline: data }));
    }, []);

    const handlePricingChange = useCallback((data: any) => {
        setFormData(prev => ({ ...prev, pricing: data }));
    }, []);

    const renderStep = () => {
        switch (currentStep) {
            case 0:
                return <CompanyInfoForm onChange={handleCompanyInfoChange} />;
            case 1:
                return <ProjectDetailsForm onChange={handleProjectDetailsChange} />;
            case 2:
                return <ScopeOfWorkForm onChange={handleScopeOfWorkChange} />;
            case 3:
                return <TimelineForm onChange={handleTimelineChange} />;
            case 4:
                return <PricingForm onChange={handlePricingChange} />;
            default:
                return null;
        }
    };

    return (
        <div className="form-wizard max-w-4xl mx-auto">
            {/* Auto-save indicator */}
            <div className="flex justify-between items-center mb-6">
                <div className="flex items-center space-x-3">
                    {isSaving ? (
                        <div className="flex items-center text-blue-400">
                            <div className="animate-spin w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full mr-2"></div>
                            <span className="text-sm">Saving...</span>
                        </div>
                    ) : lastSaved ? (
                        <div className="flex items-center text-green-400">
                            <span className="text-sm mr-2">✓</span>
                            <span className="text-sm">Saved {lastSaved.toLocaleTimeString()}</span>
                        </div>
                    ) : null}
                </div>
                <div className="text-slate-400 text-sm">
                    Auto-saves every 2 seconds
                </div>
            </div>

            <StepIndicator currentStep={currentStep} totalSteps={totalSteps} />

            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl mb-8">
                {renderStep()}
            </div>

            <div className="form-wizard-navigation flex justify-between items-center">
                {currentStep > 0 && (
                    <button
                        onClick={handlePrev}
                        className="px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 font-bold transition-all duration-300"
                    >
                        <svg className="mr-2 w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                        Back
                    </button>
                )}
                {!isLastStep() && (
                    <button
                        onClick={handleNext}
                        className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 ml-auto"
                    >
                        Next Step
                        <svg className="ml-2 w-4 h-4 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                    </button>
                )}

                {isLastStep() && (
                    <div className="ml-auto text-center">
                        <div className="bg-green-500/10 border border-green-400/30 rounded-2xl p-6">
                            <div className="text-green-400 text-2xl mb-2">✅</div>
                            <h3 className="text-lg font-bold text-green-300 mb-2">SOW Complete!</h3>
                            <p className="text-green-200 text-sm mb-4">Your Statement of Work has been automatically saved</p>
                            <button
                                onClick={() => window.location.href = '/dashboard'}
                                className="px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300"
                            >
                                View in Dashboard
                            </button>
                        </div>
                    </div>
                )}
            </div>
            <SaveDraft />
        </div>
    );
};

export default FormWizard;