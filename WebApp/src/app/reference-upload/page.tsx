'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '../components/loading-spinner';

interface UploadedTemplate {
  id: string;
  name: string;
  originalFile: File;
  markdownContent: string;
  extractedFields: string[];
  uploadDate: Date;
}

const ReferenceUploadPage = () => {
  const [dragActive, setDragActive] = useState(false);
  const [uploadedTemplate, setUploadedTemplate] = useState<UploadedTemplate | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFile(e.dataTransfer.files[0]);
    }
  }, []);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      handleFile(e.target.files[0]);
    }
  };

  const handleFile = async (file: File) => {
    if (!file.name.toLowerCase().endsWith('.docx')) {
      setError('Please upload a DOCX file only.');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      setError('File too large. Please upload a file smaller than 10MB.');
      return;
    }

    setIsProcessing(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('template', file);

      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch('/api/template/upload', {
        method: 'POST',
        body: formData,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to process template');
      }

      const result = await response.json();
      
      const template: UploadedTemplate = {
        id: result.id,
        name: file.name,
        originalFile: file,
        markdownContent: result.markdown || 'Template uploaded successfully',
        extractedFields: result.fields,
        uploadDate: new Date()
      };

      setUploadedTemplate(template);

      // Store template in localStorage for the document editor
      localStorage.setItem('uploadedTemplate', JSON.stringify({
        id: template.id,
        name: template.name,
        markdownContent: template.markdownContent,
        extractedFields: template.extractedFields
      }));

    } catch (err) {
      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          setError('Upload timed out. Please try again with a smaller file.');
        } else {
          setError(err.message);
        }
      } else {
        setError('Failed to process template. Please try again.');
      }
    } finally {
      setIsProcessing(false);
    }
  };

  const handleProceedToEditor = () => {
    router.push('/document-editor');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-2xl mb-6 shadow-2xl">
            <span className="text-2xl font-bold text-white">📤</span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-emerald-100 to-teal-200 bg-clip-text text-transparent mb-6">
            Upload Reference Template
          </h1>
          
          <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto mb-8">
            Upload your existing DOCX template and let our AI intelligently fill it with your project details
          </p>
          
          {/* Process Steps */}
          <div className="flex items-center justify-center space-x-4 mb-12">
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 ${uploadedTemplate ? 'bg-green-500' : 'bg-blue-500'} rounded-full flex items-center justify-center text-white text-sm font-bold`}>
                {uploadedTemplate ? '✓' : '1'}
              </div>
              <span className={`${uploadedTemplate ? 'text-green-400' : 'text-blue-400'} font-medium`}>Upload Template</span>
            </div>
            <div className={`w-8 h-0.5 ${uploadedTemplate ? 'bg-green-500' : 'bg-slate-600'}`}></div>
            <div className="flex items-center space-x-2">
              <div className={`w-8 h-8 ${uploadedTemplate ? 'bg-blue-500' : 'bg-slate-600'} rounded-full flex items-center justify-center text-white text-sm font-bold`}>
                2
              </div>
              <span className={`${uploadedTemplate ? 'text-blue-400' : 'text-slate-400'} font-medium`}>Edit Document</span>
            </div>
            <div className="w-8 h-0.5 bg-slate-600"></div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center text-slate-400 text-sm font-bold">3</div>
              <span className="text-slate-400">Generate SOW</span>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          {!uploadedTemplate ? (
            /* Upload Area */
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
              <div
                className={`relative border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 ${
                  dragActive 
                    ? 'border-emerald-400 bg-emerald-500/10' 
                    : 'border-slate-400 hover:border-emerald-400 hover:bg-emerald-500/5'
                }`}
                onDragEnter={handleDrag}
                onDragLeave={handleDrag}
                onDragOver={handleDrag}
                onDrop={handleDrop}
              >
                {isProcessing ? (
                  <div className="py-12">
                    <LoadingSpinner size="lg" color="white" text="Processing template..." />
                    <p className="text-slate-300 mt-4">Analyzing document structure and extracting fields...</p>
                    <div className="mt-6 bg-blue-500/10 border border-blue-400/30 rounded-xl p-4">
                      <p className="text-blue-300 text-sm">⚡ This usually takes just a few seconds</p>
                    </div>
                  </div>
                ) : (
                  <>
                    <div className="w-24 h-24 bg-gradient-to-r from-emerald-500 to-teal-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-xl">
                      <span className="text-4xl">📄</span>
                    </div>
                    
                    <h3 className="text-2xl font-bold text-white mb-4">
                      Drop your DOCX template here
                    </h3>
                    
                    <p className="text-slate-300 mb-8 max-w-md mx-auto">
                      Upload your existing Statement of Work template and we&apos;ll use it as a reference for perfect formatting
                    </p>
                    
                    <div className="space-y-4">
                      <label className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-emerald-600 to-teal-600 text-white rounded-2xl hover:from-emerald-700 hover:to-teal-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl cursor-pointer">
                        <span className="mr-2">📤</span>
                        Choose DOCX File
                        <input
                          type="file"
                          accept=".docx"
                          onChange={handleFileInput}
                          className="hidden"
                        />
                      </label>
                      
                      <p className="text-slate-400 text-sm">
                        Supported format: DOCX files only
                      </p>
                    </div>
                  </>
                )}
              </div>
              
              {error && (
                <div className="mt-6 p-4 bg-red-500/10 border border-red-400/30 rounded-2xl">
                  <div className="flex items-start">
                    <div className="text-red-400 mr-3 text-xl">⚠️</div>
                    <div>
                      <h3 className="text-red-300 font-bold mb-1">Upload Error</h3>
                      <p className="text-red-200">{error}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            /* Template Processed */
            <div className="space-y-8">
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                      <span className="text-2xl">✅</span>
                    </div>
                    <div>
                      <h2 className="text-2xl font-bold text-white">Template Processed Successfully!</h2>
                      <p className="text-slate-300">Your template is ready for AI-powered completion</p>
                    </div>
                  </div>
                  <button
                    onClick={() => setUploadedTemplate(null)}
                    className="px-4 py-2 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all duration-300"
                  >
                    Upload Different Template
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                  <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                    <h3 className="text-lg font-bold text-white mb-3">📄 Template Info</h3>
                    <div className="space-y-2 text-slate-300">
                      <p><span className="font-medium">File:</span> {uploadedTemplate.name}</p>
                      <p><span className="font-medium">Uploaded:</span> {uploadedTemplate.uploadDate.toLocaleString()}</p>
                      <p><span className="font-medium">Status:</span> <span className="text-green-400">Ready for completion</span></p>
                    </div>
                  </div>

                  <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
                    <h3 className="text-lg font-bold text-white mb-3">🔍 Detected Fields</h3>
                    <div className="flex flex-wrap gap-2">
                      {uploadedTemplate.extractedFields.map((field, index) => (
                        <span key={index} className="px-3 py-1 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-full text-sm">
                          {field}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <button
                    onClick={handleProceedToEditor}
                    className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl"
                  >
                    <span className="mr-2">✏️</span>
                    Open Document Editor
                    <svg className="ml-2 w-5 h-5 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReferenceUploadPage;
