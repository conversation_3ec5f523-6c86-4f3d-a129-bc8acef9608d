import React from 'react';
import './globals.css';
import Navigation from './components/navigation';
import { NotificationProvider } from '../contexts/NotificationContext';

export const metadata = {
  title: 'QuantumRhino SOW Generator - Automate to Elevate®',
  description: 'QuantumRhino\'s AI-powered SOW Generator. Automate to Elevate® your document creation process with intelligent automation solutions.',
  keywords: 'QuantumRhino, SOW, Statement of Work, AI, Document Generation, Automation, Professional, Automate to Elevate',
  viewport: 'width=device-width, initial-scale=1',
};

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className="antialiased font-sans bg-gradient-to-br from-slate-900 via-slate-800/95 to-slate-900 min-h-screen">
        {/* Subtle blue-pink overlay */}
        <div className="fixed inset-0 bg-gradient-to-br from-blue-900/10 via-transparent to-pink-900/10 pointer-events-none"></div>
        <NotificationProvider>
          <Navigation />
          <main className="relative">
            <div className="pt-20">
              {children}
            </div>
          </main>
        </NotificationProvider>
      </body>
    </html>
  );
}