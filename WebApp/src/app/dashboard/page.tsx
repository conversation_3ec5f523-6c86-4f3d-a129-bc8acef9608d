'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import LoadingSpinner from '../components/loading-spinner';

interface SavedSOW {
    id: string;
    title: string;
    clientName: string;
    projectName: string;
    templateName: string;
    templateId?: string;
    markdown: string;
    createdAt: string;
    status: 'draft' | 'final';
    fileSize?: number;
}

const DashboardPage = () => {
    const [stats, setStats] = useState({
        totalSOWs: 0,
        drafts: 0,
        completed: 0
    });
    const [recentSOWs, setRecentSOWs] = useState<SavedSOW[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [viewSOW, setViewSOW] = useState<SavedSOW | null>(null);
    const [showViewModal, setShowViewModal] = useState(false);
    const [showEditModal, setShowEditModal] = useState(false);
    const [editSOW, setEditSOW] = useState<SavedSOW | null>(null);
    const [editMarkdown, setEditMarkdown] = useState('');
    const [isConverting, setIsConverting] = useState(false);
    const [docxPreviewBuffer, setDocxPreviewBuffer] = useState<ArrayBuffer | null>(null);
    const [editingName, setEditingName] = useState<string | null>(null);
    const [newName, setNewName] = useState('');

    // Bulk selection state
    const [selectedSOWs, setSelectedSOWs] = useState<Set<string>>(new Set());
    const [isSelectionMode, setIsSelectionMode] = useState(false);

    useEffect(() => {
        loadSOWs();
    }, []);

    const loadSOWs = async () => {
        try {
            const response = await fetch('/api/sow/save');
            if (response.ok) {
                const sows = await response.json();
                setRecentSOWs(sows);

                // Calculate stats
                const totalSOWs = sows.length;
                const drafts = sows.filter((sow: SavedSOW) => sow.status === 'draft').length;
                const completed = sows.filter((sow: SavedSOW) => sow.status === 'final').length;

                setStats({ totalSOWs, drafts, completed });
            }
        } catch (error) {
            console.error('Failed to load SOWs:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const formatFileSize = (bytes?: number): string => {
        if (!bytes) return '0 KB';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    };

    const handleViewSOW = async (sowId: string) => {
        try {
            const response = await fetch(`/api/sow/${sowId}`);
            if (response.ok) {
                const sowData = await response.json();
                setViewSOW(sowData);

                // Generate DOCX file for preview
                await generateDocxForPreview(sowData);
                setShowViewModal(true);
            } else {
                alert('Failed to load SOW details');
            }
        } catch (error) {
            console.error('Failed to load SOW:', error);
            alert('Failed to load SOW details');
        }
    };

    const generateDocxForPreview = async (sowData: SavedSOW) => {
        try {
            console.log('🔄 DASHBOARD STEP 1: Starting generateDocxForPreview');
            console.log('SOW Data:', {
                id: sowData.id,
                templateId: sowData.templateId,
                markdownLength: sowData.markdown?.length || 0,
                title: sowData.title
            });

            if (!sowData.templateId) {
                console.warn('❌ DASHBOARD: No template ID available for DOCX preview');
                setDocxPreviewBuffer(null);
                return;
            }

            if (!sowData.markdown || sowData.markdown.length === 0) {
                console.warn('❌ DASHBOARD: No markdown content available for preview');
                setDocxPreviewBuffer(null);
                return;
            }

            console.log('🔄 DASHBOARD STEP 2: Calling conversion API...');
            console.log('Template ID:', sowData.templateId);
            console.log('Markdown length:', sowData.markdown.length);

            const docxResponse = await fetch('/api/template/convert', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
                },
                body: JSON.stringify({
                    templateId: sowData.templateId,
                    markdown: sowData.markdown
                }),
            });

            console.log('🔄 DASHBOARD STEP 3: Response received');
            console.log('Response status:', docxResponse.status);
            console.log('Response ok:', docxResponse.ok);

            if (docxResponse.ok) {
                console.log('🔄 DASHBOARD STEP 4: Converting to ArrayBuffer...');
                const docxArrayBuffer = await docxResponse.arrayBuffer();

                console.log('🔄 DASHBOARD STEP 5: Buffer validation');
                console.log('Buffer size:', docxArrayBuffer.byteLength);
                console.log('Buffer type:', docxArrayBuffer.constructor.name);
                console.log('Buffer first 10 bytes:', Array.from(new Uint8Array(docxArrayBuffer.slice(0, 10))));

                // Verify it's a valid ZIP/DOCX file
                const firstBytes = new Uint8Array(docxArrayBuffer.slice(0, 2));
                const zipSignature = Array.from(firstBytes).map(b => b.toString(16).padStart(2, '0')).join('');
                console.log('ZIP signature:', zipSignature);

                if (firstBytes[0] === 0x50 && firstBytes[1] === 0x4B) {
                    console.log('✅ DASHBOARD STEP 6: Valid ZIP signature confirmed');
                    console.log('🔄 DASHBOARD STEP 7: Setting buffer state...');
                    setDocxPreviewBuffer(docxArrayBuffer);
                    console.log('✅ DASHBOARD STEP 8: Buffer state set successfully');
                } else {
                    console.error('❌ DASHBOARD: Invalid ZIP signature:', zipSignature);
                    setDocxPreviewBuffer(null);
                }
            } else {
                console.error('❌ DASHBOARD: API call failed');
                console.error('Status:', docxResponse.status);
                console.error('Status text:', docxResponse.statusText);
                const errorText = await docxResponse.text();
                console.error('Error response:', errorText);
                setDocxPreviewBuffer(null);
            }
        } catch (error) {
            console.error('❌ DASHBOARD: Exception in generateDocxForPreview:', error);
            setDocxPreviewBuffer(null);
        }
    };

    const handleDownloadSOW = async (sowId: string, title: string) => {
        try {
            const response = await fetch(`/api/sow/${sowId}/download`);

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({ error: 'Download failed' }));
                throw new Error(errorData.error || 'Download failed');
            }

            const blob = await response.blob();
            if (blob.size === 0) {
                throw new Error('Downloaded file is empty');
            }

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = `${title.replace(/[^a-zA-Z0-9\s-]/g, '').replace(/\s+/g, '-')}.docx`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        } catch (error) {
            console.error('Download error:', error);
            alert('Download failed: ' + (error instanceof Error ? error.message : 'Unknown error'));
        }
    };

    const handleEditSOW = async (sowId: string) => {
        try {
            const response = await fetch(`/api/sow/${sowId}`);
            if (response.ok) {
                const sowData = await response.json();
                setEditSOW(sowData);
                setEditMarkdown(sowData.markdown);
                setShowEditModal(true);
            } else {
                alert('Failed to load SOW for editing');
            }
        } catch (error) {
            console.error('Failed to load SOW for editing:', error);
            alert('Failed to load SOW for editing');
        }
    };

    const handleSaveEdit = async () => {
        if (!editSOW) return;

        setIsConverting(true);
        try {
            // Update the SOW with the edited markdown
            const updateResponse = await fetch(`/api/sow/${editSOW.id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    ...editSOW,
                    markdown: editMarkdown,
                    fileSize: editMarkdown.length
                }),
            });

            if (updateResponse.ok) {
                setShowEditModal(false);
                setEditSOW(null);
                setEditMarkdown('');
                loadSOWs(); // Refresh the list
                alert('SOW updated successfully!');
            } else {
                const errorData = await updateResponse.json().catch(() => ({ error: 'Update failed' }));
                alert('Failed to update SOW: ' + errorData.error);
            }
        } catch (error) {
            console.error('Failed to update SOW:', error);
            alert('Failed to update SOW');
        } finally {
            setIsConverting(false);
        }
    };

    const handleEditName = (sowId: string, currentName: string) => {
        setEditingName(sowId);
        setNewName(currentName);
    };

    const handleSaveName = async (sowId: string) => {
        if (!newName.trim()) {
            alert('Please enter a valid name');
            return;
        }

        try {
            const response = await fetch(`/api/sow/${sowId}`);
            if (response.ok) {
                const sowData = await response.json();

                const updateResponse = await fetch(`/api/sow/${sowId}`, {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        ...sowData,
                        title: newName.trim()
                    }),
                });

                if (updateResponse.ok) {
                    setEditingName(null);
                    setNewName('');
                    loadSOWs(); // Refresh the list
                } else {
                    alert('Failed to update SOW name');
                }
            }
        } catch (error) {
            console.error('Failed to update SOW name:', error);
            alert('Failed to update SOW name');
        }
    };

    const handleCancelNameEdit = () => {
        setEditingName(null);
        setNewName('');
    };

    const handleDeleteSOW = async (sowId: string) => {
        if (!confirm('Are you sure you want to delete this SOW? This action cannot be undone.')) {
            return;
        }

        try {
            const response = await fetch(`/api/sow/${sowId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                // Reload SOWs list
                loadSOWs();
            } else {
                const errorData = await response.json().catch(() => ({ error: 'Delete failed' }));
                alert('Failed to delete SOW: ' + errorData.error);
            }
        } catch (error) {
            console.error('Failed to delete SOW:', error);
            alert('Failed to delete SOW');
        }
    };

    // Bulk selection functions
    const toggleSelectionMode = () => {
        setIsSelectionMode(!isSelectionMode);
        setSelectedSOWs(new Set());
    };

    const toggleSOWSelection = (sowId: string) => {
        const newSelected = new Set(selectedSOWs);
        if (newSelected.has(sowId)) {
            newSelected.delete(sowId);
        } else {
            newSelected.add(sowId);
        }
        setSelectedSOWs(newSelected);
    };

    const selectAllSOWs = () => {
        if (selectedSOWs.size === recentSOWs.length) {
            setSelectedSOWs(new Set());
        } else {
            setSelectedSOWs(new Set(recentSOWs.map(sow => sow.id)));
        }
    };

    const handleBulkDelete = async () => {
        if (selectedSOWs.size === 0) {
            alert('Please select SOWs to delete');
            return;
        }

        const count = selectedSOWs.size;
        if (!confirm(`Are you sure you want to delete ${count} SOW${count > 1 ? 's' : ''}? This action cannot be undone.`)) {
            return;
        }

        try {
            const deletePromises = Array.from(selectedSOWs).map(sowId =>
                fetch(`/api/sow/${sowId}`, { method: 'DELETE' })
            );

            const results = await Promise.allSettled(deletePromises);

            // Count failures properly - check both rejected promises and failed HTTP responses
            const failures = results.filter(result =>
                result.status === 'rejected' ||
                (result.status === 'fulfilled' && !result.value.ok)
            ).length;

            if (failures > 0) {
                alert(`${count - failures} SOWs deleted successfully. ${failures} failed to delete.`);
            } else {
                alert(`${count} SOW${count > 1 ? 's' : ''} deleted successfully!`);
            }

            // Reset selection and reload
            setSelectedSOWs(new Set());
            setIsSelectionMode(false);
            loadSOWs();
        } catch (error) {
            console.error('Bulk delete error:', error);
            alert('Failed to delete SOWs');
        }
    };

    const handleBulkDownload = async () => {
        if (selectedSOWs.size === 0) {
            alert('Please select SOWs to download');
            return;
        }

        const count = selectedSOWs.size;
        const selectedSOWsList = recentSOWs.filter(sow => selectedSOWs.has(sow.id));

        try {
            // Show progress indicator
            const progressDiv = document.createElement('div');
            progressDiv.innerHTML = `
                <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
                           background: rgba(0,0,0,0.8); color: white; padding: 20px; border-radius: 10px;
                           z-index: 10000; text-align: center;">
                    <div style="margin-bottom: 10px;">📥 Downloading ${count} SOW${count > 1 ? 's' : ''}...</div>
                    <div style="width: 200px; height: 4px; background: rgba(255,255,255,0.3); border-radius: 2px;">
                        <div id="download-progress" style="width: 0%; height: 100%; background: #3b82f6; border-radius: 2px; transition: width 0.3s;"></div>
                    </div>
                    <div id="download-status" style="margin-top: 10px; font-size: 12px;">Preparing downloads...</div>
                </div>
            `;
            document.body.appendChild(progressDiv);

            let completed = 0;
            const updateProgress = (status: string) => {
                const progressBar = document.getElementById('download-progress');
                const statusText = document.getElementById('download-status');
                if (progressBar) progressBar.style.width = `${(completed / count) * 100}%`;
                if (statusText) statusText.textContent = status;
            };

            // Download each SOW with a small delay to prevent overwhelming the browser
            for (const sow of selectedSOWsList) {
                try {
                    updateProgress(`Downloading: ${sow.title}`);
                    await handleDownloadSOW(sow.id, sow.title);
                    completed++;
                    updateProgress(`Downloaded: ${sow.title}`);

                    // Small delay between downloads
                    if (completed < count) {
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                } catch (error) {
                    console.error(`Failed to download SOW ${sow.title}:`, error);
                    completed++;
                    updateProgress(`Failed: ${sow.title}`);
                }
            }

            // Show completion message
            updateProgress(`✅ Downloaded ${completed} of ${count} SOWs`);

            // Remove progress indicator after a delay
            setTimeout(() => {
                document.body.removeChild(progressDiv);

                if (completed === count) {
                    alert(`${count} SOW${count > 1 ? 's' : ''} downloaded successfully!`);
                } else {
                    alert(`${completed} of ${count} SOWs downloaded. Some downloads may have failed.`);
                }
            }, 2000);

        } catch (error) {
            console.error('Bulk download error:', error);
            alert('Failed to download SOWs');
        }
    };

    return (
        <div className="min-h-screen relative overflow-hidden">


            <div className="relative z-10 container mx-auto px-6 py-24">


                {/* Stats Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12 max-w-5xl mx-auto">
                    <div className="bg-white/10 backdrop-blur-lg border border-white/20 p-8 rounded-2xl text-center hover:bg-white/15 transition-all duration-300">
                        <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Total SOWs</h3>
                        <p className="text-4xl font-bold text-blue-400 mb-2">{stats.totalSOWs}</p>
                        <p className="text-slate-400 text-sm">Documents created</p>
                    </div>

                    <div className="bg-white/10 backdrop-blur-lg border border-white/20 p-8 rounded-2xl text-center hover:bg-white/15 transition-all duration-300">
                        <div className="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Drafts</h3>
                        <p className="text-4xl font-bold text-yellow-400 mb-2">{stats.drafts}</p>
                        <p className="text-slate-400 text-sm">Work in progress</p>
                    </div>

                    <div className="bg-white/10 backdrop-blur-lg border border-white/20 p-8 rounded-2xl text-center hover:bg-white/15 transition-all duration-300">
                        <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                            <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <h3 className="text-xl font-bold text-white mb-2">Completed</h3>
                        <p className="text-4xl font-bold text-green-400 mb-2">{stats.completed}</p>
                        <p className="text-slate-400 text-sm">Ready to send</p>
                    </div>
                </div>

                {/* Recent SOWs Section */}
                <div className="max-w-6xl mx-auto">
                    <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl shadow-2xl overflow-hidden">
                        <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 p-8 border-b border-white/10">
                            <div className="flex flex-col md:flex-row justify-between items-center">
                                <div>
                                    <h2 className="text-2xl font-bold text-white mb-2">Recent SOWs</h2>
                                    <p className="text-slate-300">
                                        {isSelectionMode
                                            ? `${selectedSOWs.size} of ${recentSOWs.length} SOWs selected`
                                            : 'Your latest Statement of Work documents'
                                        }
                                    </p>
                                </div>
                                <div className="flex space-x-4 mt-4 md:mt-0">
                                    {isSelectionMode ? (
                                        <>
                                            <button
                                                onClick={selectAllSOWs}
                                                className="px-4 py-2 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-xl hover:bg-blue-500/30 font-bold transition-all duration-300"
                                            >
                                                {selectedSOWs.size === recentSOWs.length ? (
                                                    <>
                                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                        Deselect All
                                                    </>
                                                ) : (
                                                    <>
                                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                        </svg>
                                                        Select All
                                                    </>
                                                )}
                                            </button>
                                            <button
                                                onClick={handleBulkDownload}
                                                disabled={selectedSOWs.size === 0}
                                                className="px-4 py-2 bg-green-500/20 border border-green-400/30 text-green-300 rounded-xl hover:bg-green-500/30 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                </svg>
                                                Download ({selectedSOWs.size})
                                            </button>
                                            <button
                                                onClick={handleBulkDelete}
                                                disabled={selectedSOWs.size === 0}
                                                className="px-4 py-2 bg-red-500/20 border border-red-400/30 text-red-300 rounded-xl hover:bg-red-500/30 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                                            >
                                                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                </svg>
                                                Delete ({selectedSOWs.size})
                                            </button>
                                            <button
                                                onClick={toggleSelectionMode}
                                                className="px-4 py-2 bg-gray-500/20 border border-gray-400/30 text-gray-300 rounded-xl hover:bg-gray-500/30 font-bold transition-all duration-300"
                                            >
                                                Cancel
                                            </button>
                                        </>
                                    ) : (
                                        <>
                                            {recentSOWs.length > 0 && (
                                                <button
                                                    onClick={toggleSelectionMode}
                                                    className="px-4 py-2 bg-orange-500/20 border border-orange-400/30 text-orange-300 rounded-xl hover:bg-orange-500/30 font-bold transition-all duration-300"
                                                >
                                                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                                                    </svg>
                                                    Select Multiple
                                                </button>
                                            )}
                                            <Link
                                                href="/sow-generator"
                                                className="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-lg"
                                            >
                                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                                </svg>
                                                AI Generator
                                            </Link>

                                        </>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="p-8">
                            {isLoading ? (
                                <div className="text-center py-16">
                                    <div className="w-12 h-12 border-4 border-blue-500/30 border-t-blue-500 rounded-full animate-spin mx-auto mb-4"></div>
                                    <p className="text-slate-300">Loading SOWs...</p>
                                </div>
                            ) : recentSOWs.length > 0 ? (
                                <div className="space-y-4">
                                    {recentSOWs.slice(0, 10).map((sow) => (
                                        <div key={sow.id} className={`bg-white/5 border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300 ${
                                            isSelectionMode && selectedSOWs.has(sow.id) ? 'ring-2 ring-blue-400 bg-blue-500/10' : ''
                                        }`}>
                                            <div className="flex flex-col lg:flex-row lg:items-center gap-4">
                                                {/* Selection checkbox and main content */}
                                                <div className="flex items-start gap-4 flex-1 min-w-0">
                                                    {isSelectionMode && (
                                                        <div className="flex-shrink-0 pt-1">
                                                            <input
                                                                type="checkbox"
                                                                checked={selectedSOWs.has(sow.id)}
                                                                onChange={() => toggleSOWSelection(sow.id)}
                                                                className="w-5 h-5 bg-white/10 border border-white/30 rounded text-blue-600 focus:ring-blue-500 focus:ring-2"
                                                            />
                                                        </div>
                                                    )}

                                                    {/* Content area with proper overflow handling */}
                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-center space-x-3 mb-2">
                                                            <div className={`w-3 h-3 rounded-full flex-shrink-0 ${
                                                                sow.status === 'final' ? 'bg-green-400' : 'bg-yellow-400'
                                                            }`}></div>
                                                            {editingName === sow.id ? (
                                                                <div className="flex items-center gap-2 flex-1 min-w-0">
                                                                    <input
                                                                        type="text"
                                                                        value={newName}
                                                                        onChange={(e) => setNewName(e.target.value)}
                                                                        className="flex-1 px-3 py-1 bg-white/10 border border-white/20 text-white rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/50"
                                                                        placeholder="Enter SOW name..."
                                                                        autoFocus
                                                                        onKeyPress={(e) => e.key === 'Enter' && handleSaveName(sow.id)}
                                                                    />
                                                                    <button
                                                                        onClick={() => handleSaveName(sow.id)}
                                                                        className="px-3 py-1 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm flex-shrink-0"
                                                                    >
                                                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                                                        </svg>
                                                                    </button>
                                                                    <button
                                                                        onClick={handleCancelNameEdit}
                                                                        className="px-3 py-1 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm flex-shrink-0"
                                                                    >
                                                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                                                        </svg>
                                                                    </button>
                                                                </div>
                                                            ) : (
                                                                <div className="flex items-center gap-2 flex-1 min-w-0">
                                                                    <h3 className="text-lg font-bold text-white truncate">{sow.title}</h3>
                                                                    <button
                                                                        onClick={() => handleEditName(sow.id, sow.title)}
                                                                        className="px-2 py-1 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-all duration-300 text-xs flex-shrink-0"
                                                                    >
                                                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                                        </svg>
                                                                    </button>
                                                                </div>
                                                            )}
                                                            <span className={`px-2 py-1 rounded-full text-xs font-medium flex-shrink-0 ${
                                                                sow.status === 'final'
                                                                    ? 'bg-green-500/20 text-green-300 border border-green-400/30'
                                                                    : 'bg-yellow-500/20 text-yellow-300 border border-yellow-400/30'
                                                            }`}>
                                                                {sow.status === 'final' ? 'Final' : 'Draft'}
                                                            </span>
                                                        </div>

                                                        {/* Information grid with proper text truncation */}
                                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                                            <div className="min-w-0">
                                                                <span className="text-slate-400">Client:</span>
                                                                <span className="text-white ml-2 break-words">{sow.clientName}</span>
                                                            </div>
                                                            <div className="min-w-0">
                                                                <span className="text-slate-400">Project:</span>
                                                                <span className="text-white ml-2 break-words">{sow.projectName}</span>
                                                            </div>
                                                            <div className="min-w-0">
                                                                <span className="text-slate-400">Template:</span>
                                                                <span className="text-white ml-2 break-words">{sow.templateName}</span>
                                                            </div>
                                                        </div>

                                                        <div className="flex items-center space-x-4 mt-3 text-xs text-slate-400">
                                                            <span>Created: {new Date(sow.createdAt).toLocaleDateString()}</span>
                                                            <span>Size: {formatFileSize(sow.fileSize)}</span>
                                                        </div>
                                                    </div>
                                                </div>

                                                {/* Action buttons - properly separated */}
                                                {!isSelectionMode && (
                                                    <div className="flex items-center gap-2 flex-shrink-0 lg:ml-6">
                                                        <button
                                                            onClick={() => handleViewSOW(sow.id)}
                                                            className="px-3 py-2 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-lg hover:bg-blue-500/30 transition-all duration-300 text-sm whitespace-nowrap"
                                                        >
                                                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                            </svg>
                                                            View
                                                        </button>
                                                        <button
                                                            onClick={() => handleEditSOW(sow.id)}
                                                            className="px-3 py-2 bg-purple-500/20 border border-purple-400/30 text-purple-300 rounded-lg hover:bg-purple-500/30 transition-all duration-300 text-sm whitespace-nowrap"
                                                        >
                                                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                            </svg>
                                                            Edit
                                                        </button>
                                                        <button
                                                            onClick={() => handleDownloadSOW(sow.id, sow.title)}
                                                            className="px-3 py-2 bg-green-500/20 border border-green-400/30 text-green-300 rounded-lg hover:bg-green-500/30 transition-all duration-300 text-sm whitespace-nowrap"
                                                        >
                                                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                            </svg>
                                                            Download
                                                        </button>
                                                        <button
                                                            onClick={() => handleDeleteSOW(sow.id)}
                                                            className="px-3 py-2 bg-red-500/20 border border-red-400/30 text-red-300 rounded-lg hover:bg-red-500/30 transition-all duration-300 text-sm"
                                                        >
                                                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    ))}

                                    {recentSOWs.length > 10 && (
                                        <div className="text-center pt-6">
                                            <button className="px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 font-bold transition-all duration-300">
                                                View All SOWs ({recentSOWs.length})
                                            </button>
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="text-center py-16">
                                    <div className="w-24 h-24 bg-gradient-to-r from-slate-600 to-slate-700 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
                                        <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                    </div>
                                    <h3 className="text-2xl font-bold text-white mb-4">No SOWs Created Yet</h3>
                                    <p className="text-slate-400 mb-8 max-w-md mx-auto">
                                        Start creating professional Statements of Work to see them appear here.
                                        Use our AI generator to get started quickly.
                                    </p>
                                    <div className="flex justify-center">
                                        <Link
                                            href="/sow-generator"
                                            className="px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-2xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl"
                                        >
                                            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                            </svg>
                                            Create with AI
                                        </Link>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>

            {/* View SOW Modal - Much Larger Preview */}
            {showViewModal && viewSOW && (
                <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 w-full max-w-7xl h-[95vh] overflow-hidden flex flex-col">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-xl font-semibold text-white truncate">{viewSOW.title}</h2>
                            <button
                                onClick={() => setShowViewModal(false)}
                                className="w-8 h-8 bg-white/10 hover:bg-white/20 text-white rounded-lg transition-all duration-200 flex items-center justify-center text-sm"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div className="flex-1 overflow-hidden">
                            <div className="h-full flex flex-col">
                                <div className="bg-white rounded-xl overflow-hidden flex-1 shadow-xl">
                                    {(() => {
                                        console.log('🔄 DASHBOARD RENDER: Checking docxPreviewBuffer');
                                        console.log('Buffer available:', !!docxPreviewBuffer);
                                        console.log('Buffer size:', docxPreviewBuffer ? docxPreviewBuffer.byteLength : 'N/A');

                                        if (docxPreviewBuffer) {
                                            console.log('✅ DASHBOARD RENDER: Rendering DashboardDocxViewer component');
                                            console.log('Buffer size for component:', docxPreviewBuffer.byteLength);

                                            // Ultra-clean document preview (maximum space)
                                            return (
                                                <div className="h-full w-full bg-white">
                                                    <div
                                                        id="dashboard-docx-preview-container"
                                                        className="docx-preview-container w-full h-full overflow-auto"
                                                        style={{
                                                            backgroundColor: 'white',
                                                            minHeight: '85vh',
                                                            padding: '24px'
                                                        }}
                                                    >
                                                        <div className="flex items-center justify-center h-full text-gray-400">
                                                            <div className="text-center">
                                                                <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-3"></div>
                                                                <p className="text-sm">Loading document...</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <DashboardDocxViewer docxBuffer={docxPreviewBuffer} />
                                                </div>
                                            );
                                        } else {
                                            console.log('❌ DASHBOARD RENDER: No buffer, showing fallback content');
                                            return (
                                                <div className="p-4 h-full overflow-auto">
                                                    <div className="text-center py-8">
                                                        <div className="text-gray-400 mb-4">
                                                            <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                            </svg>
                                                        </div>
                                                        <h4 className="text-lg font-semibold text-gray-600 mb-2">Generating Preview...</h4>
                                                        <p className="text-gray-500 mb-4">Please wait while we prepare your document preview</p>
                                                    </div>
                                                    <div className="border-t pt-4">
                                                        <h5 className="font-semibold text-gray-700 mb-2">Raw Content:</h5>
                                                        <pre className="text-slate-700 text-sm whitespace-pre-wrap font-mono">
                                                            {viewSOW.markdown}
                                                        </pre>
                                                    </div>
                                                </div>
                                            );
                                        }
                                    })()}
                                </div>
                            </div>
                        </div>

                        <div className="flex gap-4 mt-6">
                            <button
                                onClick={() => {
                                    setShowViewModal(false);
                                    handleEditSOW(viewSOW.id);
                                }}
                                className="py-3 px-6 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-xl hover:from-purple-700 hover:to-indigo-700 font-bold transition-all duration-300"
                            >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                                Edit SOW
                            </button>
                            <button
                                onClick={() => handleDownloadSOW(viewSOW.id, viewSOW.title)}
                                className="flex-1 py-3 px-6 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 font-bold transition-all duration-300"
                            >
                                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                Download DOCX
                            </button>
                            <button
                                onClick={() => setShowViewModal(false)}
                                className="py-3 px-6 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 font-bold transition-all duration-300"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}

            {/* Edit SOW Modal */}
            {showEditModal && editSOW && (
                <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
                    <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 max-w-6xl max-h-[90vh] overflow-hidden flex flex-col w-full">
                        <div className="flex items-center justify-between mb-6">
                            <div>
                                <h2 className="text-2xl font-bold text-white">Edit SOW</h2>
                                <p className="text-slate-300">
                                    {editSOW.title} • {editSOW.clientName} • {editSOW.projectName}
                                </p>
                            </div>
                            <button
                                onClick={() => setShowEditModal(false)}
                                className="w-10 h-10 bg-red-500/20 border border-red-400/30 text-red-300 rounded-xl hover:bg-red-500/30 transition-all duration-300 flex items-center justify-center"
                            >
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <div className="flex-1 overflow-auto">
                            <div className="bg-white/5 border border-white/10 rounded-xl p-6">
                                <h3 className="text-lg font-semibold text-white mb-4">Edit Markdown Content:</h3>
                                <textarea
                                    value={editMarkdown}
                                    onChange={(e) => setEditMarkdown(e.target.value)}
                                    className="w-full h-96 bg-white/5 border border-white/10 rounded-lg p-4 text-slate-300 text-sm font-mono resize-none focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50"
                                    placeholder="Edit your SOW content in markdown format..."
                                />
                                <div className="mt-4 text-xs text-slate-400">
                                    <p className="flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                        </svg>
                                        Tip: Edit the content in markdown format. The changes will be saved and can be converted back to DOCX.
                                    </p>
                                    <p className="flex items-center">
                                        <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                        </svg>
                                        Characters: {editMarkdown.length}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div className="flex gap-4 mt-6">
                            <button
                                onClick={handleSaveEdit}
                                disabled={isConverting}
                                className="flex-1 py-3 px-6 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-xl hover:from-blue-700 hover:to-purple-700 font-bold transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                {isConverting ? (
                                    <span className="flex items-center justify-center">
                                        <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                                        Saving...
                                    </span>
                                ) : (
                                    '💾 Save Changes'
                                )}
                            </button>
                            <button
                                onClick={() => setShowEditModal(false)}
                                className="py-3 px-6 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 font-bold transition-all duration-300"
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

// Dashboard DOCX Viewer Component using static ID (SIMPLE APPROACH)
const DashboardDocxViewer = ({ docxBuffer }: { docxBuffer: ArrayBuffer }) => {
    console.log('🔄 DASHBOARD VIEWER: Component function called');
    console.log('Buffer provided:', !!docxBuffer);
    console.log('Buffer size:', docxBuffer ? docxBuffer.byteLength : 'N/A');

    const [isLoading, setIsLoading] = useState(() => {
        console.log('🔄 DASHBOARD VIEWER: useState isLoading initializer');
        return true;
    });
    const [error, setError] = useState<string | null>(() => {
        console.log('🔄 DASHBOARD VIEWER: useState error initializer');
        return null;
    });
    const containerId = 'dashboard-docx-preview-container';

    console.log('🔄 DASHBOARD VIEWER: About to define useEffect');

    useEffect(() => {
        console.log('🔄 DASHBOARD VIEWER: useEffect triggered');
        console.log('Buffer available:', !!docxBuffer);

        const loadDocxPreview = async () => {
            try {
                setIsLoading(true);
                setError(null);

                console.log('Loading dashboard DOCX preview, buffer size:', docxBuffer.byteLength);

                // Use getElementById with static ID
                const container = document.getElementById(containerId);
                console.log('Using container from getElementById:', !!container);

                if (!container) {
                    console.error('Dashboard container not found with ID:', containerId);
                    throw new Error('Dashboard preview container not found');
                }

                if (!docxBuffer || docxBuffer.byteLength === 0) {
                    throw new Error('Invalid DOCX buffer');
                }

                // Import docx-preview dynamically (EXACT SAME AS TEMPLATES)
                const { renderAsync } = await import('docx-preview');

                if (container && docxBuffer) {
                    await renderAsync(docxBuffer, container, undefined, {
                        className: 'docx-wrapper',
                        inWrapper: true,
                        ignoreWidth: false,
                        ignoreHeight: false,
                        ignoreFonts: false,
                        breakPages: true,
                        ignoreLastRenderedPageBreak: true,
                        experimental: false,
                        trimXmlDeclaration: true,
                        useBase64URL: false
                    });
                }
            } catch (err) {
                console.error('Failed to load dashboard DOCX preview:', err);
                setError(err instanceof Error ? err.message : 'Failed to load preview');
            } finally {
                setIsLoading(false);
            }
        };

        if (docxBuffer) {
            console.log('🔄 DASHBOARD VIEWER: Buffer available, starting load with retry mechanism...');

            // Retry mechanism to find the container
            let attempts = 0;
            const maxAttempts = 20;

            const tryLoadPreview = () => {
                attempts++;
                console.log(`🔄 DASHBOARD VIEWER: Attempt ${attempts}/${maxAttempts} to find container`);

                const container = document.getElementById(containerId);
                console.log(`Container found on attempt ${attempts}:`, !!container);

                // Debug: Show all elements with IDs
                if (!container && attempts === 1) {
                    console.log('All elements with IDs:',
                        Array.from(document.querySelectorAll('[id]')).map(el => el.id));
                    console.log('All divs:',
                        Array.from(document.querySelectorAll('div')).length);
                }

                if (container) {
                    console.log('✅ DASHBOARD VIEWER: Container found, loading preview...');
                    loadDocxPreview();
                } else if (attempts < maxAttempts) {
                    console.log(`❌ DASHBOARD VIEWER: Container not found, retrying in 50ms...`);
                    setTimeout(tryLoadPreview, 50);
                } else {
                    console.error('❌ DASHBOARD VIEWER: Failed to find container after', maxAttempts, 'attempts');
                    setError('Container not found after multiple attempts');
                    setIsLoading(false);
                }
            };

            // Start the retry process
            setTimeout(tryLoadPreview, 10);
        } else {
            console.log('❌ DASHBOARD VIEWER: No buffer provided');
        }
    }, [docxBuffer]);



    if (isLoading) {
        return (
            <div className="flex flex-col items-center justify-center h-96 bg-gray-50">
                <LoadingSpinner size="lg" color="blue" text="Loading SOW preview..." />
                <p className="text-sm text-gray-600 mt-4">Rendering your Statement of Work...</p>
            </div>
        );
    }

    if (error) {
        return (
            <div className="flex items-center justify-center h-96 bg-red-50 text-red-600">
                <div className="text-center p-6">
                    <div className="text-4xl mb-4">⚠️</div>
                    <p className="text-lg font-semibold mb-2">Preview Failed</p>
                    <p className="text-sm mb-4">{error}</p>
                    <p className="text-xs text-gray-500">
                        You can still download the SOW file.
                    </p>
                </div>
            </div>
        );
    }

    console.log('🔄 DASHBOARD VIEWER: Rendering component JSX');
    console.log('Container ID that will be used:', containerId);

    // This component just handles the logic - the actual container is created in parent JSX
    return null;
};

export default DashboardPage;
