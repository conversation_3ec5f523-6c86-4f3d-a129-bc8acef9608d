'use client';

import React, { useState, useEffect } from 'react';
import Card from '../../components/ui/card';

interface SOW {
    id: string;
    title: string;
    date: string;
    client: string;
}

const RecentSOWs: React.FC = () => {
    const [recentSows, setRecentSows] = useState<SOW[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchRecentSows = async () => {
            try {
                const response = await fetch('/api/sow');
                if (!response.ok) {
                    throw new Error('Failed to fetch SOWs');
                }
                const data = await response.json();
                setRecentSows(data.slice(0, 5)); // Get the 5 most recent
            } catch (err) {
                setError(err instanceof Error ? err.message : 'An error occurred');
            } finally {
                setLoading(false);
            }
        };

        fetchRecentSows();
    }, []);

    if (loading) {
        return <div>Loading...</div>;
    }

    if (error) {
        return <div>Error loading recent SOWs: {error}</div>;
    }

    return (
        <div className="recent-sows">
            <h2>Recent Statements of Work</h2>
            <div className="sow-list">
                {recentSows.map((sow) => (
                    <Card 
                        key={sow.id} 
                        title={sow.title}
                        content={
                            <div>
                                <p><strong>Date:</strong> {sow.date}</p>
                                <p><strong>Client:</strong> {sow.client}</p>
                            </div>
                        }
                        className="sow-card mb-4"
                    />
                ))}
            </div>
        </div>
    );
};

export default RecentSOWs;