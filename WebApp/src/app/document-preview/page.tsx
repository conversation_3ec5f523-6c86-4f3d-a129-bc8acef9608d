'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '../components/loading-spinner';

interface FinalDocument {
  id: string;
  fieldValues: { [key: string]: string };
  html: string;
  metadata: any;
  stats: any;
  sections: any[];
  originalName: string;
}

const DocumentPreviewPage = () => {
  const [document, setDocument] = useState<FinalDocument | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'preview' | 'markdown'>('preview');
  const router = useRouter();

  useEffect(() => {
    const stored = localStorage.getItem('finalDocument');
    if (stored) {
      setDocument(JSON.parse(stored));
    } else {
      router.push('/reference-upload');
    }
  }, [router]);

  const handleDownload = async (format: 'docx' | 'pdf') => {
    if (!document) return;

    setIsDownloading(true);
    setError(null);

    try {
      const response = await fetch('/api/template/convert', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId: document.id,
          fieldValues: document.fieldValues,
          format: format
        }),
      });

      if (!response.ok) throw new Error(`Failed to generate ${format.toUpperCase()}`);

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = window.document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `SOW-${new Date().toISOString().split('T')[0]}.${format}`;
      window.document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      window.document.body.removeChild(a);

    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to download ${format.toUpperCase()}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleSaveAsTemplate = async () => {
    if (!document) return;

    try {
      const response = await fetch('/api/templates/save', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          name: `${document.originalName} - ${new Date().toLocaleDateString()}`,
          fieldValues: document.fieldValues,
          sections: document.sections
        }),
      });

      if (response.ok) {
        alert('Template saved successfully!');
      }
    } catch (error) {
      console.error('Failed to save template:', error);
    }
  };

  const handleStartOver = () => {
    localStorage.removeItem('finalDocument');
    localStorage.removeItem('uploadedTemplate');
    router.push('/reference-upload');
  };

  if (!document) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <LoadingSpinner size="lg" color="white" text="Loading document..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-2xl">
            <span className="text-3xl">🎉</span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-green-100 to-emerald-200 bg-clip-text text-transparent mb-6">
            Document Complete!
          </h1>
          
          <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
            Your professional Statement of Work has been generated with {document.stats.completionPercentage}% completion rate
          </p>

          {/* Stats */}
          <div className="flex flex-wrap justify-center gap-8 mb-8 text-slate-400">
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{document.stats.completedFields}/{document.stats.totalFields}</div>
              <div className="text-sm">Fields Completed</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white">{document.sections.length}</div>
              <div className="text-sm">Sections</div>
            </div>
            {document.stats.totalCost && (
              <div className="text-center">
                <div className="text-2xl font-bold text-green-400">${document.stats.totalCost}</div>
                <div className="text-sm">Project Value</div>
              </div>
            )}
            {document.stats.durationDays && (
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-400">{document.stats.durationDays}</div>
                <div className="text-sm">Days Duration</div>
              </div>
            )}
          </div>
        </div>

        <div className="max-w-7xl mx-auto space-y-8">
          {/* Action Bar */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-6 shadow-2xl">
            <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setViewMode('preview')}
                  className={`px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
                    viewMode === 'preview'
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'
                  }`}
                >
                  📄 Preview
                </button>
                <button
                  onClick={() => setViewMode('markdown')}
                  className={`px-6 py-3 rounded-xl font-bold transition-all duration-300 ${
                    viewMode === 'markdown'
                      ? 'bg-blue-600 text-white shadow-lg'
                      : 'bg-white/10 border border-white/20 text-white hover:bg-white/20'
                  }`}
                >
                  📝 Markdown
                </button>
              </div>

              <div className="flex flex-wrap gap-4">
                <button
                  onClick={() => handleDownload('docx')}
                  disabled={isDownloading}
                  className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-xl hover:from-blue-700 hover:to-blue-800 disabled:from-gray-600 disabled:to-gray-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl"
                >
                  📄 Download DOCX
                </button>
                
                <button
                  onClick={() => handleDownload('pdf')}
                  disabled={isDownloading}
                  className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-xl hover:from-red-700 hover:to-red-800 disabled:from-gray-600 disabled:to-gray-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl"
                >
                  📑 Download PDF
                </button>

                <button
                  onClick={handleSaveAsTemplate}
                  className="px-6 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl hover:from-purple-700 hover:to-purple-800 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl"
                >
                  💾 Save as Template
                </button>

                <button
                  onClick={handleStartOver}
                  className="px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 font-bold transition-all duration-300"
                >
                  ← Start Over
                </button>
              </div>
            </div>
          </div>

          {/* Document Display */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl shadow-2xl overflow-hidden">
            {viewMode === 'preview' ? (
              <div className="p-8">
                <div 
                  className="bg-white rounded-2xl shadow-2xl overflow-hidden"
                  dangerouslySetInnerHTML={{ __html: document.html }}
                />
              </div>
            ) : (
              <div className="p-8">
                <div className="bg-slate-900 rounded-2xl p-6 border border-white/10">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold text-white">Markdown Source</h3>
                    <button
                      onClick={() => {
                        const fieldValuesText = Object.entries(document.fieldValues)
                          .map(([key, value]) => `${key}: ${value}`)
                          .join('\n');
                        navigator.clipboard.writeText(fieldValuesText);
                        alert('Field values copied to clipboard!');
                      }}
                      className="px-4 py-2 bg-blue-500/20 border border-blue-400/30 text-blue-300 rounded-lg text-sm hover:bg-blue-500/30 transition-all duration-300"
                    >
                      📋 Copy Field Values
                    </button>
                  </div>
                  <div className="bg-slate-800 rounded-lg p-4 overflow-auto max-h-96">
                    <h4 className="text-white font-bold mb-4">Field Values:</h4>
                    {Object.entries(document.fieldValues).map(([key, value]) => (
                      <div key={key} className="mb-2 p-2 bg-slate-700 rounded">
                        <span className="text-blue-300 font-mono text-sm">{key}:</span>
                        <span className="text-white ml-2">{value}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Project Summary */}
          {document.stats.totalCost && (
            <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
              <h3 className="text-2xl font-bold text-white mb-6">📊 Project Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
                {document.stats.totalCost && (
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400">${document.stats.totalCost}</div>
                    <div className="text-slate-400">Total Cost</div>
                  </div>
                )}
                {document.stats.estimatedHours && (
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-400">{document.stats.estimatedHours}h</div>
                    <div className="text-slate-400">Estimated Hours</div>
                  </div>
                )}
                {document.stats.hourlyRate && (
                  <div className="text-center">
                    <div className="text-3xl font-bold text-purple-400">${document.stats.hourlyRate}</div>
                    <div className="text-slate-400">Hourly Rate</div>
                  </div>
                )}
                {document.stats.durationDays && (
                  <div className="text-center">
                    <div className="text-3xl font-bold text-orange-400">{document.stats.durationDays}d</div>
                    <div className="text-slate-400">Duration</div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div className="p-6 bg-red-500/10 border border-red-400/30 rounded-2xl">
              <div className="flex items-start">
                <div className="text-red-400 mr-4 text-2xl">⚠️</div>
                <div>
                  <h3 className="text-red-300 font-bold text-lg mb-2">Download Error</h3>
                  <p className="text-red-200">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentPreviewPage;
