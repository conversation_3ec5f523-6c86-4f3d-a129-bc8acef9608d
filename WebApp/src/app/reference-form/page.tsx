'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '../components/loading-spinner';

interface TemplateData {
  id: string;
  name: string;
  markdownContent: string;
  extractedFields: string[];
}

interface FormData {
  // Essential Info
  clientCompany: string;
  clientName: string;
  clientEmail: string;
  projectName: string;
  
  // Pricing Info
  hourlyRate: number;
  estimatedHours: number;
  
  // Auto-calculated
  totalCost: number;
  startDate: string;
  endDate: string;
  
  // Additional fields that might be extracted from template
  [key: string]: any;
}

const ReferenceFormPage = () => {
  const [templateData, setTemplateData] = useState<TemplateData | null>(null);
  const [formData, setFormData] = useState<FormData>({
    clientCompany: '',
    clientName: '',
    clientEmail: '',
    projectName: '',
    hourlyRate: 150,
    estimatedHours: 40,
    totalCost: 6000,
    startDate: new Date().toISOString().split('T')[0],
    endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    // Load template data from localStorage
    const stored = localStorage.getItem('uploadedTemplate');
    if (stored) {
      setTemplateData(JSON.parse(stored));
    } else {
      router.push('/reference-upload');
    }
  }, [router]);

  useEffect(() => {
    // Auto-calculate total cost
    setFormData(prev => ({
      ...prev,
      totalCost: prev.hourlyRate * prev.estimatedHours
    }));
  }, [formData.hourlyRate, formData.estimatedHours]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleGenerate = async () => {
    if (!templateData) return;

    setIsGenerating(true);
    setError(null);

    try {
      const response = await fetch('/api/template/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId: templateData.id,
          formData: formData,
          markdownTemplate: templateData.markdownContent
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate SOW');
      }

      const result = await response.json();
      
      // Store result and redirect to preview
      localStorage.setItem('generatedSOW', JSON.stringify(result));
      router.push('/reference-preview');

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate SOW');
    } finally {
      setIsGenerating(false);
    }
  };

  if (!templateData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <LoadingSpinner size="lg" color="white" text="Loading template..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-6 shadow-2xl">
            <span className="text-2xl font-bold text-white">✏️</span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent mb-6">
            Fill Your Template
          </h1>
          
          <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
            Provide minimal details and watch AI intelligently complete your <span className="text-blue-400 font-semibold">{templateData.name}</span> template
          </p>
          
          {/* Progress Steps */}
          <div className="flex items-center justify-center space-x-4 mb-8">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">✓</div>
              <span className="text-green-400 font-medium">Upload Template</span>
            </div>
            <div className="w-8 h-0.5 bg-green-500"></div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
              <span className="text-blue-400 font-medium">Fill Details</span>
            </div>
            <div className="w-8 h-0.5 bg-slate-600"></div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center text-slate-400 text-sm font-bold">3</div>
              <span className="text-slate-400">Generate SOW</span>
            </div>
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl shadow-2xl overflow-hidden">
            {/* Form Header */}
            <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 p-6 border-b border-white/10">
              <h2 className="text-2xl font-bold text-white mb-2">Project Information</h2>
              <p className="text-slate-300">AI will auto-fill the rest based on these details</p>
            </div>

            <div className="p-8 space-y-8">
              {/* Essential Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">1</span>
                  </div>
                  <h3 className="text-xl font-bold text-white">Essential Information</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-white mb-3">Client Company *</label>
                    <input
                      type="text"
                      value={formData.clientCompany}
                      onChange={(e) => handleInputChange('clientCompany', e.target.value)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                      placeholder="Enter client company name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-3">Client Contact Name *</label>
                    <input
                      type="text"
                      value={formData.clientName}
                      onChange={(e) => handleInputChange('clientName', e.target.value)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                      placeholder="Enter contact person name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-3">Client Email *</label>
                    <input
                      type="email"
                      value={formData.clientEmail}
                      onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                      placeholder="Enter client email"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-3">Project Name *</label>
                    <input
                      type="text"
                      value={formData.projectName}
                      onChange={(e) => handleInputChange('projectName', e.target.value)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                      placeholder="Enter project name"
                    />
                  </div>
                </div>
              </div>

              {/* Pricing Information */}
              <div className="space-y-6">
                <div className="flex items-center space-x-3 mb-6">
                  <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center">
                    <span className="text-white text-sm font-bold">2</span>
                  </div>
                  <h3 className="text-xl font-bold text-white">Pricing & Timeline</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-white mb-3">Hourly Rate ($)</label>
                    <input
                      type="number"
                      value={formData.hourlyRate}
                      onChange={(e) => handleInputChange('hourlyRate', parseInt(e.target.value) || 0)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                      min="1"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-3">Estimated Hours</label>
                    <input
                      type="number"
                      value={formData.estimatedHours}
                      onChange={(e) => handleInputChange('estimatedHours', parseInt(e.target.value) || 0)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                      min="1"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-3">Start Date</label>
                    <input
                      type="date"
                      value={formData.startDate}
                      onChange={(e) => handleInputChange('startDate', e.target.value)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-white mb-3">End Date</label>
                    <input
                      type="date"
                      value={formData.endDate}
                      onChange={(e) => handleInputChange('endDate', e.target.value)}
                      className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                    />
                  </div>
                </div>

                {/* Auto-calculated total */}
                <div className="bg-green-500/10 border border-green-400/30 rounded-2xl p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-lg font-bold text-green-300 mb-1">Total Project Cost</h4>
                      <p className="text-green-200 text-sm">Automatically calculated: {formData.hourlyRate} × {formData.estimatedHours} hours</p>
                    </div>
                    <div className="text-3xl font-bold text-green-400">
                      ${formData.totalCost.toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>

              {/* Generate Button */}
              <div className="pt-8 text-center">
                <button
                  onClick={handleGenerate}
                  disabled={!formData.clientCompany || !formData.clientName || !formData.projectName || isGenerating}
                  className={`px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform ${
                    formData.clientCompany && formData.clientName && formData.projectName && !isGenerating
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-2xl hover:shadow-blue-500/25 hover:scale-[1.02]'
                      : 'bg-slate-600 text-slate-400 cursor-not-allowed'
                  }`}
                >
                  {isGenerating ? (
                    <LoadingSpinner size="md" color="white" text="AI is filling your template..." />
                  ) : (
                    <span className="flex items-center justify-center">
                      <span className="mr-2">🤖</span>
                      Generate AI-Completed SOW
                      <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                      </svg>
                    </span>
                  )}
                </button>
              </div>

              {/* Error Display */}
              {error && (
                <div className="mt-6 p-6 bg-red-500/10 border border-red-400/30 rounded-2xl">
                  <div className="flex items-start">
                    <div className="text-red-400 mr-4 text-2xl">⚠️</div>
                    <div>
                      <h3 className="text-red-300 font-bold text-lg mb-2">Generation Error</h3>
                      <p className="text-red-200">{error}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferenceFormPage;
