'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import LoadingSpinner from '../components/loading-spinner';

interface GeneratedSOW {
  id: string;
  markdown: string;
  formData: any;
  aiContent: any;
  generatedAt: string;
}

const ReferencePreviewPage = () => {
  const [generatedSOW, setGeneratedSOW] = useState<GeneratedSOW | null>(null);
  const [isDownloading, setIsDownloading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const stored = localStorage.getItem('generatedSOW');
    if (stored) {
      setGeneratedSOW(JSON.parse(stored));
    } else {
      router.push('/reference-upload');
    }
  }, [router]);

  const handleDownload = async (format: 'docx' | 'pdf') => {
    if (!generatedSOW) return;

    setIsDownloading(true);
    setError(null);

    try {
      const response = await fetch('/api/template/convert', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          templateId: generatedSOW.id,
          markdown: generatedSOW.markdown,
          format: format
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate ${format.toUpperCase()}`);
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `SOW-${generatedSOW.formData.clientCompany}-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to download ${format.toUpperCase()}`);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleStartOver = () => {
    localStorage.removeItem('generatedSOW');
    localStorage.removeItem('uploadedTemplate');
    router.push('/reference-upload');
  };

  if (!generatedSOW) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <LoadingSpinner size="lg" color="white" text="Loading generated SOW..." />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-12">
        {/* Success Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-2xl">
            <span className="text-3xl">🎉</span>
          </div>
          
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-green-100 to-emerald-200 bg-clip-text text-transparent mb-6">
            SOW Generated Successfully!
          </h1>
          
          <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
            Your AI-completed Statement of Work for <span className="text-green-400 font-semibold">{generatedSOW.formData.clientCompany}</span> is ready for download in your original template format.
          </p>
          
          {/* Progress Indicator - Completed */}
          <div className="flex items-center justify-center space-x-4 mb-12">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">✓</div>
              <span className="text-green-400 font-medium">Upload Template</span>
            </div>
            <div className="w-8 h-0.5 bg-green-500"></div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">✓</div>
              <span className="text-green-400 font-medium">Fill Details</span>
            </div>
            <div className="w-8 h-0.5 bg-green-500"></div>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">✓</div>
              <span className="text-green-400 font-medium">Generate SOW</span>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto space-y-8">
          {/* Download Actions */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
            <div className="flex flex-col lg:flex-row justify-between items-center mb-6">
              <div className="text-center lg:text-left mb-6 lg:mb-0">
                <h2 className="text-2xl font-bold text-white mb-2">Download Your SOW</h2>
                <p className="text-slate-300">Choose your preferred format - your original template formatting will be preserved</p>
              </div>
              <button
                onClick={handleStartOver}
                className="px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all duration-300 font-medium"
              >
                ← Create Another SOW
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <button
                onClick={() => handleDownload('docx')}
                disabled={isDownloading}
                className="group relative px-8 py-6 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl hover:from-blue-700 hover:to-blue-800 disabled:from-gray-600 disabled:to-gray-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-blue-500/25"
              >
                <div className="flex items-center justify-center">
                  <span className="text-3xl mr-4">📄</span>
                  <div className="text-left">
                    <div className="text-xl mb-1">Download DOCX</div>
                    <div className="text-sm opacity-80">Original template format preserved</div>
                    <div className="text-xs opacity-60 mt-1">Perfect for editing and sharing</div>
                  </div>
                </div>
              </button>
              
              <button
                onClick={() => handleDownload('pdf')}
                disabled={isDownloading}
                className="group relative px-8 py-6 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-2xl hover:from-red-700 hover:to-red-800 disabled:from-gray-600 disabled:to-gray-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-red-500/25"
              >
                <div className="flex items-center justify-center">
                  <span className="text-3xl mr-4">📑</span>
                  <div className="text-left">
                    <div className="text-xl mb-1">Download PDF</div>
                    <div className="text-sm opacity-80">Professional presentation format</div>
                    <div className="text-xs opacity-60 mt-1">Ready to send to clients</div>
                  </div>
                </div>
              </button>
            </div>

            {/* Project Summary */}
            <div className="bg-white/5 rounded-2xl p-6 border border-white/10">
              <h3 className="text-lg font-bold text-white mb-4">📊 Project Summary</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-400">${generatedSOW.formData.totalCost.toLocaleString()}</div>
                  <div className="text-slate-400 text-sm">Total Cost</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">{generatedSOW.formData.estimatedHours}h</div>
                  <div className="text-slate-400 text-sm">Estimated Hours</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-400">${generatedSOW.formData.hourlyRate}</div>
                  <div className="text-slate-400 text-sm">Hourly Rate</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-400">
                    {Math.ceil((new Date(generatedSOW.formData.endDate).getTime() - new Date(generatedSOW.formData.startDate).getTime()) / (1000 * 60 * 60 * 24))}d
                  </div>
                  <div className="text-slate-400 text-sm">Duration</div>
                </div>
              </div>
            </div>
          </div>

          {/* SOW Preview */}
          <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-bold text-white">Document Preview</h3>
              <span className="px-4 py-2 bg-green-500/20 border border-green-400/30 text-green-300 rounded-full text-sm font-medium">
                ✓ AI-Completed
              </span>
            </div>
            
            <div className="bg-slate-900/50 rounded-2xl p-6 border border-white/10">
              <div className="markdown-content whitespace-pre-wrap font-mono text-sm text-slate-300 overflow-auto max-h-96 leading-relaxed">
                {generatedSOW.markdown}
              </div>
            </div>
            
            <div className="mt-6 text-center">
              <p className="text-slate-400 text-sm">
                This preview shows your completed SOW. Download above to get the properly formatted document.
              </p>
            </div>
          </div>

          {/* Error Display */}
          {error && (
            <div className="p-6 bg-red-500/10 border border-red-400/30 rounded-2xl">
              <div className="flex items-start">
                <div className="text-red-400 mr-4 text-2xl">⚠️</div>
                <div>
                  <h3 className="text-red-300 font-bold text-lg mb-2">Download Error</h3>
                  <p className="text-red-200">{error}</p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ReferencePreviewPage;
