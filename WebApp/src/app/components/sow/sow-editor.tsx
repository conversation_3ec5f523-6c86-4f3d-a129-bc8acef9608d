'use client';

import React, { useState } from 'react';
import CompanyInfoForm from '../forms/company-info-form';
import ProjectDetailsForm from '../forms/project-details-form';
import ScopeOfWorkForm from '../forms/scope-of-work-form';
import TimelineForm from '../forms/timeline-form';
import PricingForm from '../forms/pricing-form';
import Button from '../ui/button';

const SOWEditor = () => {
    const [companyInfo, setCompanyInfo] = useState({});
    const [projectDetails, setProjectDetails] = useState({});
    const [scopeOfWork, setScopeOfWork] = useState({});
    const [timeline, setTimeline] = useState({});
    const [pricing, setPricing] = useState({});

    const handleSave = () => {
        const sowData = {
            companyInfo,
            projectDetails,
            scopeOfWork,
            timeline,
            pricing,
        };
        console.log('SOW Data:', sowData);
        // Here you would typically send the data to your API or state management
    };

    return (
        <div className="sow-editor">
            <h1>Create Statement of Work</h1>
            <CompanyInfoForm onChange={setCompanyInfo} />
            <ProjectDetailsForm onChange={setProjectDetails} />
            <ScopeOfWorkForm onChange={setScopeOfWork} />
            <TimelineForm onChange={setTimeline} />
            <PricingForm onChange={setPricing} />
            <Button onClick={handleSave}>Save SOW</Button>
        </div>
    );
};

export default SOWEditor;