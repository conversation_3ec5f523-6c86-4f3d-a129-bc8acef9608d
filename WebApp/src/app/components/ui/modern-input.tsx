import React, { useState } from 'react';

interface ModernInputProps {
  label?: string;
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  variant?: 'default' | 'glass' | 'outlined' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  error?: string;
  disabled?: boolean;
  required?: boolean;
  className?: string;
}

const ModernInput: React.FC<ModernInputProps> = ({
  label,
  placeholder,
  value,
  onChange,
  type = 'text',
  variant = 'default',
  size = 'md',
  icon,
  iconPosition = 'left',
  error,
  disabled = false,
  required = false,
  className = ''
}) => {
  const [focused, setFocused] = useState(false);

  const baseClasses = 'w-full transition-all duration-300 focus:outline-none';

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  };

  const variantClasses = {
    default: 'bg-white border border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20',
    glass: 'bg-white/10 backdrop-blur-xl border border-white/20 rounded-xl text-white placeholder-white/60 focus:border-white/40 focus:ring-4 focus:ring-white/20',
    outlined: 'bg-transparent border-2 border-gray-300 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-500/20',
    minimal: 'bg-gray-50 border-0 rounded-xl focus:bg-white focus:ring-4 focus:ring-blue-500/20'
  };

  const iconClasses = {
    sm: 'w-4 h-4',
    md: 'w-5 h-5',
    lg: 'w-6 h-6'
  };

  const labelClasses = variant === 'glass' ? 'text-white/80' : 'text-gray-700';
  const errorClasses = variant === 'glass' ? 'text-red-300' : 'text-red-500';

  return (
    <div className={`space-y-2 ${className}`}>
      {label && (
        <label className={`block text-sm font-medium ${labelClasses}`}>
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      <div className="relative">
        {icon && iconPosition === 'left' && (
          <div className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${iconClasses[size]} ${variant === 'glass' ? 'text-white/60' : 'text-gray-400'}`}>
            {icon}
          </div>
        )}
        
        <input
          type={type}
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          required={required}
          onFocus={() => setFocused(true)}
          onBlur={() => setFocused(false)}
          className={`
            ${baseClasses}
            ${sizeClasses[size]}
            ${variantClasses[variant]}
            ${icon && iconPosition === 'left' ? 'pl-10' : ''}
            ${icon && iconPosition === 'right' ? 'pr-10' : ''}
            ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500/20' : ''}
            ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
          `}
        />
        
        {icon && iconPosition === 'right' && (
          <div className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${iconClasses[size]} ${variant === 'glass' ? 'text-white/60' : 'text-gray-400'}`}>
            {icon}
          </div>
        )}
      </div>
      
      {error && (
        <p className={`text-sm ${errorClasses} animate-slide-in-up`}>
          {error}
        </p>
      )}
    </div>
  );
};

export default ModernInput;
