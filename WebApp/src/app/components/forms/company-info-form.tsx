'use client';

import React, { useState, useEffect } from 'react';
import Input from '../ui/input';
import Button from '../ui/button';

interface CompanyInfoFormProps {
    onChange?: (data: any) => void;
}

const CompanyInfoForm: React.FC<CompanyInfoFormProps> = ({ onChange }) => {
    const [companyName, setCompanyName] = useState('');
    const [contactName, setContactName] = useState('');
    const [email, setEmail] = useState('');
    const [phone, setPhone] = useState('');

    useEffect(() => {
        if (onChange) {
            onChange({
                companyName,
                contactName,
                email,
                phone,
            });
        }
    }, [companyName, contactName, email, phone, onChange]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const companyInfo = {
            companyName,
            contactName,
            email,
            phone,
        };
        console.log('Company Info Submitted:', companyInfo);
        // Here you can add logic to handle the submission, like sending it to an API
    };

    return (
        <form onSubmit={handleSubmit} className="company-info-form">
            <h2>Company Information</h2>
            <Input
                label="Company Name"
                value={companyName}
                onChange={(e) => setCompanyName(e.target.value)}
                placeholder="Enter your company name"
            />
            <Input
                label="Contact Name"
                value={contactName}
                onChange={(e) => setContactName(e.target.value)}
                placeholder="Enter your contact name"
            />
            <Input
                label="Email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
            />
            <Input
                label="Phone"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                placeholder="Enter your phone number"
            />
            <Button type="submit">Submit</Button>
        </form>
    );
};

export default CompanyInfoForm;