'use client';

import React, { useState, useEffect } from 'react';
import Button from '../ui/button';
import Input from '../ui/input';
import Select from '../ui/select';
import Textarea from '../ui/textarea';

interface ProjectDetailsFormProps {
    onChange?: (data: any) => void;
}

const ProjectDetailsForm: React.FC<ProjectDetailsFormProps> = ({ onChange }) => {
    const [projectName, setProjectName] = useState('');
    const [projectType, setProjectType] = useState('');
    const [projectComplexity, setProjectComplexity] = useState('');
    const [projectDescription, setProjectDescription] = useState('');

    useEffect(() => {
        if (onChange) {
            onChange({
                projectName,
                projectType,
                projectComplexity,
                projectDescription,
            });
        }
    }, [projectName, projectType, projectComplexity, projectDescription, onChange]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle form submission logic here
        console.log({
            projectName,
            projectType,
            projectComplexity,
            projectDescription,
        });
    };

    return (
        <form onSubmit={handleSubmit}>
            <Input
                label="Project Name"
                value={projectName}
                onChange={(e) => setProjectName(e.target.value)}
                placeholder="Enter project name"
            />
            <div>
                <label className="block text-sm font-medium mb-1">Project Type</label>
                <Select
                    value={projectType}
                    onChange={(value: string) => setProjectType(value)}
                    options={[
                        { value: '', label: 'Select project type' },
                        { value: 'development', label: 'Development' },
                        { value: 'consulting', label: 'Consulting' },
                        { value: 'design', label: 'Design' },
                    ]}
                />
            </div>
            <div>
                <label className="block text-sm font-medium mb-1">Project Complexity</label>
                <Select
                    value={projectComplexity}
                    onChange={(value: string) => setProjectComplexity(value)}
                    options={[
                        { value: '', label: 'Select complexity level' },
                        { value: 'low', label: 'Low' },
                        { value: 'medium', label: 'Medium' },
                        { value: 'high', label: 'High' },
                    ]}
                />
            </div>
            <Textarea
                label="Project Description"
                value={projectDescription}
                onChange={(e) => setProjectDescription(e.target.value)}
                placeholder="Enter project description"
            />
            <Button type="submit">Submit</Button>
        </form>
    );
};

export default ProjectDetailsForm;