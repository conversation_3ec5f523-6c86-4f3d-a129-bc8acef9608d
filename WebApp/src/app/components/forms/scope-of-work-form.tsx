'use client';

import React, { useState, useEffect } from 'react';
import Button from '../ui/button';
import Input from '../ui/input';
import Textarea from '../ui/textarea';
import Select from '../ui/select';

interface ScopeOfWorkFormProps {
    onChange?: (data: any) => void;
}

const ScopeOfWorkForm: React.FC<ScopeOfWorkFormProps> = ({ onChange }) => {
    const [scopeDetails, setScopeDetails] = useState({
        projectName: '',
        description: '',
        deliverables: '',
        timeline: '',
        resources: '',
    });

    useEffect(() => {
        if (onChange) {
            onChange(scopeDetails);
        }
    }, [scopeDetails, onChange]);

    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setScopeDetails({
            ...scopeDetails,
            [name]: value,
        });
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle form submission logic here
        console.log('Scope of Work submitted:', scopeDetails);
    };

    return (
        <form onSubmit={handleSubmit} className="scope-of-work-form">
            <Input
                name="projectName"
                value={scopeDetails.projectName}
                onChange={handleChange}
                placeholder="Project Name"
                required
            />
            <Textarea
                name="description"
                value={scopeDetails.description}
                onChange={handleChange}
                placeholder="Project Description"
                required
            />
            <Textarea
                name="deliverables"
                value={scopeDetails.deliverables}
                onChange={handleChange}
                placeholder="Deliverables"
                required
            />
            <Input
                name="timeline"
                value={scopeDetails.timeline}
                onChange={handleChange}
                placeholder="Timeline"
                required
            />
            <div>
                <label className="block text-sm font-medium mb-1">Resources</label>
                <Select
                    value={scopeDetails.resources}
                    onChange={(value: string) => setScopeDetails({...scopeDetails, resources: value})}
                    options={[
                        { value: 'resource1', label: 'Resource 1' },
                        { value: 'resource2', label: 'Resource 2' },
                        { value: 'resource3', label: 'Resource 3' },
                    ]}
                />
            </div>
            <Button type="submit">Submit Scope of Work</Button>
        </form>
    );
};

export default ScopeOfWorkForm;