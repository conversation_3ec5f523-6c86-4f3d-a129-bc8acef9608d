'use client';

import React, { useState } from 'react';
import LoadingSpinner from '../loading-spinner';

interface QuickSOWFormProps {
  onGenerate: (data: QuickSOWData) => void;
  isLoading?: boolean;
}

export interface QuickSOWData {
  companyName: string;
  projectType: string;
  budget: string;
  timeline?: string;
  clientName?: string;
  clientEmail?: string;
  projectDescription?: string;
}

const PROJECT_TYPES = [
  { value: 'web-application', label: 'Web Application' },
  { value: 'mobile-application', label: 'Mobile Application' },
  { value: 'e-commerce', label: 'E-commerce Platform' },
  { value: 'api-development', label: 'API Development' },
  { value: 'database-design', label: 'Database Design' },
  { value: 'consulting', label: 'Technical Consulting' },
];

const BUDGET_RANGES = [
  { value: '10000', label: '$10,000 - $25,000' },
  { value: '25000', label: '$25,000 - $50,000' },
  { value: '50000', label: '$50,000 - $100,000' },
  { value: '100000', label: '$100,000 - $250,000' },
  { value: '250000', label: '$250,000+' },
  { value: 'custom', label: 'Custom Amount' },
];

const QuickSOWForm: React.FC<QuickSOWFormProps> = ({ onGenerate, isLoading = false }) => {
  const [formData, setFormData] = useState<QuickSOWData>({
    companyName: '',
    projectType: 'web-application',
    budget: '50000',
    timeline: '',
    clientName: '',
    clientEmail: '',
    projectDescription: '',
  });

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [customBudget, setCustomBudget] = useState('');

  const handleInputChange = (field: keyof QuickSOWData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const finalData = {
      ...formData,
      budget: formData.budget === 'custom' ? customBudget : formData.budget
    };

    onGenerate(finalData);
  };

  const isFormValid = formData.companyName.trim() !== '' && 
                     formData.projectType !== '' && 
                     (formData.budget !== 'custom' || customBudget.trim() !== '');

  return (
    <div className="max-w-3xl mx-auto">
      <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl shadow-2xl overflow-hidden">
        <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 p-8 text-center border-b border-white/10">
          <h2 className="text-3xl font-bold text-white mb-2">
            Quick SOW Generator
          </h2>
          <p className="text-slate-300">
            Generate a professional Statement of Work in seconds with minimal input
          </p>
        </div>

        <form onSubmit={handleSubmit} className="p-8 space-y-8">
          {/* Essential Fields */}
          <div className="space-y-6">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                <span className="text-white text-sm font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold text-white">
                Essential Information
              </h3>
            </div>

            {/* Company Name */}
            <div>
              <label htmlFor="companyName" className="block text-sm font-medium text-white mb-3">
                Client Company Name *
              </label>
              <input
                type="text"
                id="companyName"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                placeholder="Enter client company name"
                required
              />
            </div>

            {/* Project Type */}
            <div>
              <label htmlFor="projectType" className="block text-sm font-medium text-white mb-3">
                Project Type *
              </label>
              <select
                id="projectType"
                value={formData.projectType}
                onChange={(e) => handleInputChange('projectType', e.target.value)}
                className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                required
              >
                {PROJECT_TYPES.map(type => (
                  <option key={type.value} value={type.value} className="bg-slate-800 text-white">
                    {type.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Budget */}
            <div>
              <label htmlFor="budget" className="block text-sm font-medium text-white mb-3">
                Project Budget *
              </label>
              <select
                id="budget"
                value={formData.budget}
                onChange={(e) => handleInputChange('budget', e.target.value)}
                className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                required
              >
                {BUDGET_RANGES.map(range => (
                  <option key={range.value} value={range.value} className="bg-slate-800 text-white">
                    {range.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Custom Budget Input */}
            {formData.budget === 'custom' && (
              <div>
                <label htmlFor="customBudget" className="block text-sm font-medium text-white mb-3">
                  Custom Budget Amount *
                </label>
                <input
                  type="number"
                  id="customBudget"
                  value={customBudget}
                  onChange={(e) => setCustomBudget(e.target.value)}
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                  placeholder="Enter budget amount"
                  min="1000"
                  required
                />
              </div>
            )}
          </div>

          {/* Advanced Fields Toggle */}
          <div className="border-t border-white/10 pt-6">
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center text-blue-400 hover:text-blue-300 font-medium transition-colors duration-300"
            >
              <span className="mr-3 text-lg">
                {showAdvanced ? '▼' : '▶'}
              </span>
              Advanced Options (Optional)
            </button>
          </div>

          {/* Advanced Fields */}
          {showAdvanced && (
            <div className="space-y-6 bg-white/5 border border-white/10 p-6 rounded-2xl">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-bold">2</span>
                </div>
                <h3 className="text-xl font-bold text-white">
                  Additional Details
                </h3>
              </div>

              {/* Timeline */}
              <div>
                <label htmlFor="timeline" className="block text-sm font-medium text-white mb-3">
                  Project Timeline
                </label>
                <input
                  type="text"
                  id="timeline"
                  value={formData.timeline}
                  onChange={(e) => handleInputChange('timeline', e.target.value)}
                  className="w-full px-4 py-4 bg-white/10 border border-white/20 rounded-xl text-white placeholder-slate-400 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:bg-white/15 transition-all duration-300"
                  placeholder="e.g., 12 weeks, 3 months"
                />
              </div>

            {/* Client Name */}
            <div>
              <label htmlFor="clientName" className="block text-sm font-medium text-gray-700 mb-2">
                Primary Contact Name
              </label>
              <input
                type="text"
                id="clientName"
                value={formData.clientName}
                onChange={(e) => handleInputChange('clientName', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Contact person name"
              />
            </div>

            {/* Client Email */}
            <div>
              <label htmlFor="clientEmail" className="block text-sm font-medium text-gray-700 mb-2">
                Primary Contact Email
              </label>
              <input
                type="email"
                id="clientEmail"
                value={formData.clientEmail}
                onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="<EMAIL>"
              />
            </div>

            {/* Project Description */}
            <div>
              <label htmlFor="projectDescription" className="block text-sm font-medium text-gray-700 mb-2">
                Project Description
              </label>
              <textarea
                id="projectDescription"
                value={formData.projectDescription}
                onChange={(e) => handleInputChange('projectDescription', e.target.value)}
                rows={3}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Brief description of the project requirements..."
              />
            </div>
          </div>
        )}

          {/* Submit Button */}
          <div className="pt-8">
            <button
              type="submit"
              disabled={!isFormValid || isLoading}
              className={`w-full py-5 px-8 rounded-2xl font-bold text-lg transition-all duration-300 transform ${
                isFormValid && !isLoading
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-2xl hover:shadow-blue-500/25 hover:scale-[1.02]'
                  : 'bg-slate-600 text-slate-400 cursor-not-allowed'
              }`}
            >
              {isLoading ? (
                <LoadingSpinner
                  size="md"
                  color="white"
                  text="Generating Your Professional SOW..."
                />
              ) : (
                <span className="flex items-center justify-center">
                  <span className="mr-2">⚡</span>
                  Generate Professional SOW
                  <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                  </svg>
                </span>
              )}
            </button>
          </div>

          {/* Help Text */}
          <div className="text-center text-slate-400 mt-6">
            <p className="text-sm leading-relaxed">
              Our AI will automatically generate a comprehensive Statement of Work
              with detailed phases, pricing breakdown, and professional terms.
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};

export default QuickSOWForm;
