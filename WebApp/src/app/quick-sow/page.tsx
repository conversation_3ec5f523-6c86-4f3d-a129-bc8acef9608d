'use client';

import React, { useState } from 'react';
import <PERSON><PERSON><PERSON><PERSON><PERSON>, { QuickSOWData } from '../components/forms/quick-sow-form';

interface GeneratedSOW {
  markdown: string;
  data: any;
}

const QuickSOWPage = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [generatedSOW, setGeneratedSOW] = useState<GeneratedSOW | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleGenerateSOW = async (formData: QuickSOWData) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/sow/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          outputFormat: 'markdown'
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to generate SOW');
      }

      const result = await response.json();
      setGeneratedSOW({
        markdown: result.markdown,
        data: result.data
      });

    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDownload = async (format: 'docx' | 'pdf') => {
    if (!generatedSOW) return;

    setIsLoading(true);
    try {
      const response = await fetch('/api/sow/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          companyName: generatedSOW.data.clientInfo.clientCompany,
          projectType: generatedSOW.data.projectDetails.projectType,
          budget: generatedSOW.data.pricing.totalBudget.toString(),
          outputFormat: format
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to generate ${format.toUpperCase()}`);
      }

      // Download the file
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.style.display = 'none';
      a.href = url;
      a.download = `SOW-${generatedSOW.data.clientInfo.clientCompany}-${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to download ${format.toUpperCase()}`);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartOver = () => {
    setGeneratedSOW(null);
    setError(null);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900">
      <div className="container mx-auto px-4 py-8">
        {!generatedSOW ? (
          <>
            {/* Header */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl mb-6 shadow-2xl">
                <span className="text-2xl font-bold text-white">⚡</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-100 to-purple-200 bg-clip-text text-transparent mb-6">
                Quick SOW Generator
              </h1>

              <p className="text-xl md:text-2xl text-slate-300 max-w-4xl mx-auto mb-8 leading-relaxed">
                Generate professional Statements of Work in seconds. Just enter your client&apos;s company name,
                select the project type, and set a budget - we&apos;ll handle the rest with intelligent autofill.
              </p>

              {/* Progress Indicator */}
              <div className="flex items-center justify-center space-x-4 mb-8">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                  <span className="text-blue-400 font-medium">Input Details</span>
                </div>
                <div className="w-8 h-0.5 bg-slate-600"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center text-slate-400 text-sm font-bold">2</div>
                  <span className="text-slate-400">Generate SOW</span>
                </div>
                <div className="w-8 h-0.5 bg-slate-600"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-slate-600 rounded-full flex items-center justify-center text-slate-400 text-sm font-bold">3</div>
                  <span className="text-slate-400">Download</span>
                </div>
              </div>
            </div>

            {/* Features */}
            <div className="grid md:grid-cols-3 gap-8 mb-12 max-w-5xl mx-auto">
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 p-8 rounded-2xl text-center hover:bg-white/15 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <span className="text-2xl">⚡</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">Lightning Fast</h3>
                <p className="text-slate-300">Generate complete SOWs in under 30 seconds with AI-powered automation</p>
              </div>
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 p-8 rounded-2xl text-center hover:bg-white/15 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <span className="text-2xl">🎯</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">Smart Autofill</h3>
                <p className="text-slate-300">AI-powered content generation based on project type and industry standards</p>
              </div>
              <div className="bg-white/10 backdrop-blur-lg border border-white/20 p-8 rounded-2xl text-center hover:bg-white/15 transition-all duration-300">
                <div className="w-16 h-16 bg-gradient-to-r from-green-400 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <span className="text-2xl">📄</span>
                </div>
                <h3 className="text-xl font-bold text-white mb-3">Multiple Formats</h3>
                <p className="text-slate-300">Export to DOCX, PDF, or Markdown with professional styling</p>
              </div>
            </div>

            {/* Form */}
            <QuickSOWForm onGenerate={handleGenerateSOW} isLoading={isLoading} />

            {/* Error Display */}
            {error && (
              <div className="max-w-3xl mx-auto mt-8 p-6 bg-red-500/10 border border-red-400/30 rounded-2xl backdrop-blur-lg">
                <div className="flex items-start">
                  <div className="text-red-400 mr-4 text-2xl">⚠️</div>
                  <div>
                    <h3 className="text-red-300 font-bold text-lg mb-2">Generation Error</h3>
                    <p className="text-red-200">{error}</p>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <>
            {/* Success Header */}
            <div className="text-center mb-12">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full mb-6 shadow-2xl">
                <span className="text-3xl">🎉</span>
              </div>

              <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-green-100 to-emerald-200 bg-clip-text text-transparent mb-6">
                SOW Generated Successfully!
              </h1>

              <p className="text-xl text-slate-300 max-w-3xl mx-auto mb-8">
                Your professional Statement of Work for <span className="text-green-400 font-semibold">{generatedSOW.data.clientInfo.clientCompany}</span> is ready for download.
              </p>

              {/* Progress Indicator - Completed */}
              <div className="flex items-center justify-center space-x-4 mb-12">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">✓</div>
                  <span className="text-green-400 font-medium">Input Details</span>
                </div>
                <div className="w-8 h-0.5 bg-green-500"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">✓</div>
                  <span className="text-green-400 font-medium">Generate SOW</span>
                </div>
                <div className="w-8 h-0.5 bg-green-500"></div>
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                  <span className="text-blue-400 font-medium">Download</span>
                </div>
              </div>
            </div>

            {/* Generated SOW Display */}
            <div className="max-w-7xl mx-auto">
              {/* Download Actions */}
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 mb-8 shadow-2xl">
                <div className="flex flex-col lg:flex-row justify-between items-center mb-6">
                  <div className="text-center lg:text-left mb-6 lg:mb-0">
                    <h2 className="text-2xl font-bold text-white mb-2">Ready for Download</h2>
                    <p className="text-slate-300">Choose your preferred format and download your professional SOW</p>
                  </div>
                  <button
                    onClick={handleStartOver}
                    className="px-6 py-3 bg-white/10 border border-white/20 text-white rounded-xl hover:bg-white/20 transition-all duration-300 font-medium"
                  >
                    ← Create Another SOW
                  </button>
                </div>

                {/* Download Buttons */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <button
                    onClick={() => handleDownload('docx')}
                    disabled={isLoading}
                    className="group relative px-8 py-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-2xl hover:from-blue-700 hover:to-blue-800 disabled:from-gray-600 disabled:to-gray-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-blue-500/25"
                  >
                    <div className="flex items-center justify-center">
                      <span className="text-2xl mr-3">📄</span>
                      <div className="text-left">
                        <div className="text-lg">Download DOCX</div>
                        <div className="text-sm opacity-80">Microsoft Word format</div>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => handleDownload('pdf')}
                    disabled={isLoading}
                    className="group relative px-8 py-4 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-2xl hover:from-red-700 hover:to-red-800 disabled:from-gray-600 disabled:to-gray-700 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-red-500/25"
                  >
                    <div className="flex items-center justify-center">
                      <span className="text-2xl mr-3">📑</span>
                      <div className="text-left">
                        <div className="text-lg">Download PDF</div>
                        <div className="text-sm opacity-80">Portable document format</div>
                      </div>
                    </div>
                  </button>

                  <button
                    onClick={() => {
                      const blob = new Blob([generatedSOW.markdown], { type: 'text/markdown' });
                      const url = window.URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `SOW-${generatedSOW.data.clientInfo.clientCompany}-${new Date().toISOString().split('T')[0]}.md`;
                      a.click();
                      window.URL.revokeObjectURL(url);
                    }}
                    className="group relative px-8 py-4 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-2xl hover:from-gray-700 hover:to-gray-800 font-bold transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-gray-500/25"
                  >
                    <div className="flex items-center justify-center">
                      <span className="text-2xl mr-3">📝</span>
                      <div className="text-left">
                        <div className="text-lg">Download Markdown</div>
                        <div className="text-sm opacity-80">Raw text format</div>
                      </div>
                    </div>
                  </button>
                </div>
              </div>

              {/* SOW Preview */}
              <div className="bg-white/10 backdrop-blur-xl border border-white/20 rounded-3xl p-8 shadow-2xl">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-2xl font-bold text-white">Document Preview</h3>
                  <span className="px-4 py-2 bg-green-500/20 border border-green-400/30 text-green-300 rounded-full text-sm font-medium">
                    ✓ Generated
                  </span>
                </div>

                <div className="bg-slate-900/50 rounded-2xl p-6 border border-white/10">
                  <div className="markdown-content whitespace-pre-wrap font-mono text-sm text-slate-300 overflow-auto max-h-96 leading-relaxed">
                    {generatedSOW.markdown}
                  </div>
                </div>

                <div className="mt-6 text-center">
                  <p className="text-slate-400 text-sm">
                    This is a preview of your generated SOW. Download in your preferred format above.
                  </p>
                </div>
              </div>

              {/* Error Display */}
              {error && (
                <div className="mt-8 p-6 bg-red-500/10 border border-red-400/30 rounded-2xl">
                  <div className="flex items-start">
                    <div className="text-red-400 mr-4 text-2xl">⚠️</div>
                    <div>
                      <h3 className="text-red-300 font-bold text-lg mb-2">Download Error</h3>
                      <p className="text-red-200">{error}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default QuickSOWPage;
