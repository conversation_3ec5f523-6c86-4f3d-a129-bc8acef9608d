// Database functions for SOW management
// Note: Using dynamic imports to handle Prisma client availability

export interface SOWData {
  id?: number;
  projectName: string;
  clientName: string;
  vendorName: string;
  scopeOfWork: string;
  timeline: string;
  pricing: string;
  companyId: number;
}

// Initialize Prisma client with error handling
async function getPrismaClient() {
  try {
    const { PrismaClient } = await import('@prisma/client');
    return new PrismaClient();
  } catch {
    console.warn('Prisma client not available, using mock implementation');
    return null;
  }
}

export async function createSOW(data: Omit<SOWData, 'id'>) {
  try {
    const prisma = await getPrismaClient();
    if (!prisma) {
      // Mock implementation
      return {
        id: Date.now(),
        ...data,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    }
    
    const sow = await prisma.sOW.create({
      data,
    });
    return sow;
  } catch (error) {
    console.error('Error creating SOW:', error);
    throw new Error('Failed to create SOW');
  }
}

export async function getSOWs() {
  try {
    const prisma = await getPrismaClient();
    if (!prisma) {
      // Mock implementation
      return [];
    }
    
    const sows = await prisma.sOW.findMany({
      include: {
        company: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
    return sows;
  } catch (error) {
    console.error('Error fetching SOWs:', error);
    throw new Error('Failed to fetch SOWs');
  }
}

export async function getSOWById(id: number) {
  try {
    const prisma = await getPrismaClient();
    if (!prisma) {
      // Mock implementation
      return null;
    }
    
    const sow = await prisma.sOW.findUnique({
      where: { id },
      include: {
        company: true,
      },
    });
    return sow;
  } catch (error) {
    console.error('Error fetching SOW:', error);
    throw new Error('Failed to fetch SOW');
  }
}

export async function updateSOW(id: number, data: Partial<SOWData>) {
  try {
    const prisma = await getPrismaClient();
    if (!prisma) {
      // Mock implementation
      return { id, ...data, updatedAt: new Date() };
    }
    
    const sow = await prisma.sOW.update({
      where: { id },
      data,
    });
    return sow;
  } catch (error) {
    console.error('Error updating SOW:', error);
    throw new Error('Failed to update SOW');
  }
}

export async function deleteSOW(id: number) {
  try {
    const prisma = await getPrismaClient();
    if (!prisma) {
      // Mock implementation - just return
      return;
    }
    
    await prisma.sOW.delete({
      where: { id },
    });
  } catch (error) {
    console.error('Error deleting SOW:', error);
    throw new Error('Failed to delete SOW');
  }
}
