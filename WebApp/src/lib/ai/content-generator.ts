// Mock AI content generator until proper AI integration is set up
export interface ProjectDetails {
  projectName: string;
  scope: string;
  timeline: string;
  budget?: string;
  requirements?: string[];
}

export interface CompanyInfo {
  name: string;
  address?: string;
  email?: string;
  phone?: string;
}

export async function generateSOWContent(
  projectDetails: ProjectDetails,
  companyInfo: CompanyInfo
): Promise<string> {
  // This is a mock implementation
  // In a real application, you would integrate with OpenAI or another AI service
  
  const sowTemplate = `
# Statement of Work

## Project Overview
**Project Name:** ${projectDetails.projectName}
**Client:** ${companyInfo.name}
**Timeline:** ${projectDetails.timeline}

## Scope of Work
${projectDetails.scope}

## Requirements
${projectDetails.requirements ? projectDetails.requirements.map(req => `- ${req}`).join('\n') : 'Requirements to be defined.'}

## Timeline
The project is expected to be completed within ${projectDetails.timeline}.

${projectDetails.budget ? `## Budget\n$${projectDetails.budget}` : ''}

## Company Information
**Name:** ${companyInfo.name}
${companyInfo.address ? `**Address:** ${companyInfo.address}` : ''}
${companyInfo.email ? `**Email:** ${companyInfo.email}` : ''}
${companyInfo.phone ? `**Phone:** ${companyInfo.phone}` : ''}

---
*This SOW was generated automatically. Please review and customize as needed.*
`;

  return sowTemplate.trim();
}

export async function generateSOWSuggestions(): Promise<string[]> {
  // Mock suggestions - in a real app, this would use AI
  return [
    "Consider adding specific deliverables and milestones",
    "Define acceptance criteria for each phase",
    "Include payment terms and schedule",
    "Add change request procedures",
    "Specify intellectual property ownership"
  ];
}
