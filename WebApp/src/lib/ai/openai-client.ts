// Mock OpenAI client - replace with actual implementation when needed
export interface OpenAIConfig {
  apiKey: string;
  model?: string;
}

export class OpenAIClient {
  private apiKey: string;
  private model: string;

  constructor(config: OpenAIConfig) {
    this.apiKey = config.apiKey;
    this.model = config.model || 'gpt-3.5-turbo';
  }

  async generateCompletion(prompt: string): Promise<string> {
    // Mock implementation - replace with actual OpenAI API call
    console.log('Mock OpenAI call with prompt:', prompt);
    
    // Return a mock response based on the prompt content
    if (prompt.toLowerCase().includes('sow') || prompt.toLowerCase().includes('statement of work')) {
      return `This is a mock AI response for SOW generation. In a real implementation, this would call the OpenAI API with your prompt: "${prompt.substring(0, 100)}..."`;
    }
    
    return `Mock AI response: I understand you want me to help with "${prompt.substring(0, 50)}...". This is a placeholder response until you configure the actual OpenAI API.`;
  }

  async generateSuggestions(context: string): Promise<string[]> {
    // Mock suggestions
    return [
      `Based on the context: "${context.substring(0, 30)}...", here are some suggestions:`,
      "Consider adding more specific details",
      "Include relevant industry standards",
      "Add measurable success criteria",
      "Specify timeline milestones"
    ];
  }
}

// Export a default instance
export const openaiClient = new OpenAIClient({
  apiKey: process.env.OPENAI_API_KEY || 'mock-api-key',
  model: 'gpt-3.5-turbo'
});
