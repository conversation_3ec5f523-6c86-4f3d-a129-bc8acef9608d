import { exec } from 'child_process';
import { promisify } from 'util';
import fs from 'fs/promises';
import path from 'path';

const execAsync = promisify(exec);

export interface PandocOptions {
  outputFormat?: 'docx' | 'pdf' | 'html';
  template?: string;
  referenceDoc?: string;
  toc?: boolean;
  numberSections?: boolean;
  standalone?: boolean;
  metadata?: Record<string, string>;
}

export class PandocService {
  private static readonly TEMP_DIR = '/tmp/sow-generator';
  private static readonly REFERENCE_DOCX = path.join(process.cwd(), 'assets', 'reference.docx');

  static async convertMarkdownToDocx(
    markdown: string, 
    outputPath: string, 
    options: PandocOptions = {}
  ): Promise<string> {
    try {
      // Ensure temp directory exists
      await this.ensureTempDir();

      // Create temporary markdown file
      const tempMarkdownPath = path.join(this.TEMP_DIR, `sow-${Date.now()}.md`);
      await fs.writeFile(tempMarkdownPath, markdown, 'utf8');

      // Build pandoc command
      const pandocArgs = await this.buildPandocArgs(tempMarkdownPath, outputPath, options);
      const command = `pandoc ${pandocArgs.join(' ')}`;

      console.log('Executing pandoc command:', command);

      // Execute pandoc
      const { stdout, stderr } = await execAsync(command);
      
      if (stderr) {
        console.warn('Pandoc warnings:', stderr);
      }

      // Clean up temp file
      await fs.unlink(tempMarkdownPath).catch(() => {});

      return outputPath;
    } catch (error) {
      console.error('Pandoc conversion error:', error);
      throw new Error(`Failed to convert markdown to DOCX: ${error}`);
    }
  }

  static async convertMarkdownToPdf(
    markdown: string, 
    outputPath: string, 
    options: PandocOptions = {}
  ): Promise<string> {
    try {
      await this.ensureTempDir();

      const tempMarkdownPath = path.join(this.TEMP_DIR, `sow-${Date.now()}.md`);
      await fs.writeFile(tempMarkdownPath, markdown, 'utf8');

      const pandocArgs = await this.buildPandocArgs(tempMarkdownPath, outputPath, {
        ...options,
        outputFormat: 'pdf'
      });
      
      // Add PDF-specific options
      pandocArgs.push('--pdf-engine=xelatex');
      pandocArgs.push('--variable=geometry:margin=1in');
      
      const command = `pandoc ${pandocArgs.join(' ')}`;
      console.log('Executing pandoc command:', command);

      const { stdout, stderr } = await execAsync(command);
      
      if (stderr) {
        console.warn('Pandoc warnings:', stderr);
      }

      await fs.unlink(tempMarkdownPath).catch(() => {});

      return outputPath;
    } catch (error) {
      console.error('Pandoc PDF conversion error:', error);
      throw new Error(`Failed to convert markdown to PDF: ${error}`);
    }
  }

  private static async buildPandocArgs(
    inputPath: string,
    outputPath: string,
    options: PandocOptions
  ): Promise<string[]> {
    const args = [
      `"${inputPath}"`,
      '-o', `"${outputPath}"`,
    ];

    // Output format specific options
    if (options.outputFormat === 'docx' || !options.outputFormat) {
      args.push('-t', 'docx');

      // Use reference document for styling if available
      if (options.referenceDoc && await this.fileExists(options.referenceDoc)) {
        args.push('--reference-doc', `"${options.referenceDoc}"`);
      }

      // Add content control anchor preservation options
      args.push('--wrap=preserve');
      args.push('--preserve-tabs');
    } else if (options.outputFormat === 'pdf') {
      args.push('-t', 'latex');
    }

    // Common options
    if (options.standalone !== false) {
      args.push('--standalone');
    }

    if (options.toc) {
      args.push('--toc');
    }

    if (options.numberSections) {
      args.push('--number-sections');
    }

    // Add metadata
    if (options.metadata) {
      Object.entries(options.metadata).forEach(([key, value]) => {
        args.push('--metadata', `${key}="${value}"`);
      });
    }

    // Add default styling for DOCX
    if (options.outputFormat === 'docx') {
      args.push('--highlight-style', 'tango');
    }

    return args;
  }

  private static async ensureTempDir(): Promise<void> {
    try {
      await fs.access(this.TEMP_DIR);
    } catch {
      await fs.mkdir(this.TEMP_DIR, { recursive: true });
    }
  }

  private static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  static async createReferenceDocument(): Promise<void> {
    // Create a basic reference document with QuantumRhino styling
    const referenceMarkdown = `---
title: "QuantumRhino Reference Document"
author: "QuantumRhino"
---

# Heading 1

This is a sample document to establish styling for QuantumRhino SOW documents.

## Heading 2

Sample paragraph text with **bold** and *italic* formatting.

### Heading 3

- Bullet point 1
- Bullet point 2
- Bullet point 3

#### Heading 4

1. Numbered list item 1
2. Numbered list item 2
3. Numbered list item 3

**Table Example:**

| Column 1 | Column 2 | Column 3 |
|----------|----------|----------|
| Data 1   | Data 2   | Data 3   |
| Data 4   | Data 5   | Data 6   |

> This is a blockquote example.

\`\`\`
Code block example
\`\`\`
`;

    const assetsDir = path.join(process.cwd(), 'assets');
    await fs.mkdir(assetsDir, { recursive: true });

    const tempMarkdownPath = path.join(this.TEMP_DIR, 'reference.md');
    await fs.writeFile(tempMarkdownPath, referenceMarkdown, 'utf8');

    const referenceDocxPath = path.join(assetsDir, 'reference.docx');
    
    try {
      await this.convertMarkdownToDocx(referenceMarkdown, referenceDocxPath, {
        outputFormat: 'docx',
        standalone: true
      });
      console.log('Reference document created at:', referenceDocxPath);
    } catch (error) {
      console.error('Failed to create reference document:', error);
    }
  }

  static getDefaultDocxOptions(): PandocOptions {
    return {
      outputFormat: 'docx',
      standalone: true,
      toc: true,
      numberSections: false,
      metadata: {
        'title': 'Statement of Work',
        'author': 'QuantumRhino',
        'company': 'QuantumRhino'
      }
    };
  }

  static getDefaultPdfOptions(): PandocOptions {
    return {
      outputFormat: 'pdf',
      standalone: true,
      toc: true,
      numberSections: true,
      metadata: {
        'title': 'Statement of Work',
        'author': 'QuantumRhino',
        'geometry': 'margin=1in',
        'fontsize': '11pt',
        'linestretch': '1.15'
      }
    };
  }

  static async generateSOWDocx(
    markdown: string, 
    clientName: string = 'Client'
  ): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `SOW-${clientName.replace(/[^a-zA-Z0-9]/g, '')}-${timestamp}.docx`;
    const outputPath = path.join(this.TEMP_DIR, filename);

    await this.convertMarkdownToDocx(markdown, outputPath, this.getDefaultDocxOptions());
    
    return outputPath;
  }

  static async generateSOWPdf(
    markdown: string, 
    clientName: string = 'Client'
  ): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `SOW-${clientName.replace(/[^a-zA-Z0-9]/g, '')}-${timestamp}.pdf`;
    const outputPath = path.join(this.TEMP_DIR, filename);

    await this.convertMarkdownToPdf(markdown, outputPath, this.getDefaultPdfOptions());
    
    return outputPath;
  }

  static async cleanupTempFiles(olderThanHours: number = 24): Promise<void> {
    try {
      const files = await fs.readdir(this.TEMP_DIR);
      const cutoffTime = Date.now() - (olderThanHours * 60 * 60 * 1000);

      for (const file of files) {
        const filePath = path.join(this.TEMP_DIR, file);
        const stats = await fs.stat(filePath);
        
        if (stats.mtime.getTime() < cutoffTime) {
          await fs.unlink(filePath);
          console.log(`Cleaned up old temp file: ${file}`);
        }
      }
    } catch (error) {
      console.error('Error cleaning up temp files:', error);
    }
  }
}
