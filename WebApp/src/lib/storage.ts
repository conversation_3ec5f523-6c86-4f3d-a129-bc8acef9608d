import fs from 'fs-extra';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import mime from 'mime-types';

// Storage configuration
const STORAGE_ROOT = process.env.STORAGE_PATH || '/app/storage';
const TEMPLATES_DIR = path.join(STORAGE_ROOT, 'templates');
const SOWS_DIR = path.join(STORAGE_ROOT, 'sows');
const UPLOADS_DIR = path.join(STORAGE_ROOT, 'uploads');

// Ensure directories exist
export async function initializeStorage() {
  try {
    await fs.ensureDir(STORAGE_ROOT);
    await fs.ensureDir(TEMPLATES_DIR);
    await fs.ensureDir(SOWS_DIR);
    await fs.ensureDir(UPLOADS_DIR);
    console.log('✅ Storage directories initialized');
  } catch (error) {
    console.error('❌ Failed to initialize storage:', error);
    throw error;
  }
}

// File storage utilities
export class FileStorage {
  
  /**
   * Store a template file for a specific user
   */
  static async storeTemplate(
    userId: string, 
    file: Buffer, 
    originalName: string,
    mimeType?: string
  ): Promise<{ filePath: string; fileName: string; fileSize: number }> {
    const fileId = uuidv4();
    const extension = path.extname(originalName);
    const fileName = `${fileId}${extension}`;
    const userTemplatesDir = path.join(TEMPLATES_DIR, userId);
    const filePath = path.join(userTemplatesDir, fileName);
    
    // Ensure user template directory exists
    await fs.ensureDir(userTemplatesDir);
    
    // Write file
    await fs.writeFile(filePath, file);
    
    return {
      filePath: path.relative(STORAGE_ROOT, filePath),
      fileName: originalName,
      fileSize: file.length
    };
  }

  /**
   * Store a generated SOW file
   */
  static async storeSOW(
    userId: string,
    sowId: string,
    file: Buffer,
    fileName: string
  ): Promise<{ filePath: string; fileSize: number }> {
    const userSOWsDir = path.join(SOWS_DIR, userId);
    const filePath = path.join(userSOWsDir, `${sowId}_${fileName}`);
    
    // Ensure user SOW directory exists
    await fs.ensureDir(userSOWsDir);
    
    // Write file
    await fs.writeFile(filePath, file);
    
    return {
      filePath: path.relative(STORAGE_ROOT, filePath),
      fileSize: file.length
    };
  }

  /**
   * Get file from storage
   */
  static async getFile(relativePath: string): Promise<Buffer> {
    const fullPath = path.join(STORAGE_ROOT, relativePath);
    
    if (!await fs.pathExists(fullPath)) {
      throw new Error('File not found');
    }
    
    return await fs.readFile(fullPath);
  }

  /**
   * Delete file from storage
   */
  static async deleteFile(relativePath: string): Promise<void> {
    const fullPath = path.join(STORAGE_ROOT, relativePath);
    
    if (await fs.pathExists(fullPath)) {
      await fs.remove(fullPath);
    }
  }

  /**
   * Get file stats
   */
  static async getFileStats(relativePath: string) {
    const fullPath = path.join(STORAGE_ROOT, relativePath);
    
    if (!await fs.pathExists(fullPath)) {
      throw new Error('File not found');
    }
    
    const stats = await fs.stat(fullPath);
    return {
      size: stats.size,
      created: stats.birthtime,
      modified: stats.mtime
    };
  }

  /**
   * List user templates
   */
  static async listUserTemplates(userId: string): Promise<string[]> {
    const userTemplatesDir = path.join(TEMPLATES_DIR, userId);
    
    if (!await fs.pathExists(userTemplatesDir)) {
      return [];
    }
    
    const files = await fs.readdir(userTemplatesDir);
    return files.filter(file => !file.startsWith('.'));
  }

  /**
   * List user SOWs
   */
  static async listUserSOWs(userId: string): Promise<string[]> {
    const userSOWsDir = path.join(SOWS_DIR, userId);
    
    if (!await fs.pathExists(userSOWsDir)) {
      return [];
    }
    
    const files = await fs.readdir(userSOWsDir);
    return files.filter(file => !file.startsWith('.'));
  }

  /**
   * Clean up old files (for maintenance)
   */
  static async cleanupOldFiles(daysOld: number = 30): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);
    
    // This would be implemented for cleanup tasks
    // For now, just log the action
    console.log(`🧹 Cleanup task would remove files older than ${cutoffDate}`);
  }

  /**
   * Get storage usage for a user
   */
  static async getUserStorageUsage(userId: string): Promise<{
    templates: { count: number; size: number };
    sows: { count: number; size: number };
    total: number;
  }> {
    const userTemplatesDir = path.join(TEMPLATES_DIR, userId);
    const userSOWsDir = path.join(SOWS_DIR, userId);
    
    let templatesSize = 0;
    let templatesCount = 0;
    let sowsSize = 0;
    let sowsCount = 0;
    
    // Calculate templates usage
    if (await fs.pathExists(userTemplatesDir)) {
      const templateFiles = await fs.readdir(userTemplatesDir);
      for (const file of templateFiles) {
        if (!file.startsWith('.')) {
          const stats = await fs.stat(path.join(userTemplatesDir, file));
          templatesSize += stats.size;
          templatesCount++;
        }
      }
    }
    
    // Calculate SOWs usage
    if (await fs.pathExists(userSOWsDir)) {
      const sowFiles = await fs.readdir(userSOWsDir);
      for (const file of sowFiles) {
        if (!file.startsWith('.')) {
          const stats = await fs.stat(path.join(userSOWsDir, file));
          sowsSize += stats.size;
          sowsCount++;
        }
      }
    }
    
    return {
      templates: { count: templatesCount, size: templatesSize },
      sows: { count: sowsCount, size: sowsSize },
      total: templatesSize + sowsSize
    };
  }
}

// Helper function to format file sizes
export function formatFileSize(bytes: number): string {
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

// Helper function to get MIME type
export function getMimeType(filename: string): string {
  return mime.lookup(filename) || 'application/octet-stream';
}
