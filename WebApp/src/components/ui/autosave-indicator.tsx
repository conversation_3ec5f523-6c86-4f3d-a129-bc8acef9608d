'use client';

import React from 'react';

interface AutosaveIndicatorProps {
  isSaving: boolean;
  lastSaved: Date | null;
  hasUnsavedChanges: boolean;
  className?: string;
}

const AutosaveIndicator: React.FC<AutosaveIndicatorProps> = ({
  isSaving,
  lastSaved,
  hasUnsavedChanges,
  className = ''
}) => {
  const getStatusInfo = () => {
    if (isSaving) {
      return {
        icon: <div className="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin" />,
        text: 'Saving...',
        color: 'text-blue-400'
      };
    }
    
    if (hasUnsavedChanges) {
      return {
        icon: <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />,
        text: 'Unsaved changes',
        color: 'text-yellow-400'
      };
    }
    
    if (lastSaved) {
      return {
        icon: <div className="w-2 h-2 bg-green-400 rounded-full" />,
        text: `Saved ${formatTime(lastSaved)}`,
        color: 'text-green-400'
      };
    }
    
    return null;
  };

  const formatTime = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    if (diffInSeconds < 60) {
      return 'just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes}m ago`;
    } else {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
  };

  const statusInfo = getStatusInfo();
  
  if (!statusInfo) return null;

  return (
    <div className={`flex items-center space-x-2 text-sm ${statusInfo.color} ${className}`}>
      {statusInfo.icon}
      <span>{statusInfo.text}</span>
    </div>
  );
};

export default AutosaveIndicator;
