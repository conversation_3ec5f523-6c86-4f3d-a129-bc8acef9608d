:root {
    --primary-color: #4A90E2;
    --secondary-color: #50E3C2;
    --accent-color: #F5A623;
    --background-color: #F7F7F7;
    --text-color: #333333;
    --border-radius: 4px;
    --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.hidden {
    display: none;
}

.flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.align-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

.p-10 {
    padding: 10px;
}

.bg-primary {
    background-color: var(--primary-color);
}

.bg-secondary {
    background-color: var(--secondary-color);
}

.text-primary {
    color: var(--primary-color);
}

.text-secondary {
    color: var(--secondary-color);
}

.border {
    border: 1px solid #E0E0E0;
}

.border-radius {
    border-radius: var(--border-radius);
}

.box-shadow {
    box-shadow: var(--box-shadow);
}

.btn {
    padding: 10px 20px;
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: darken(var(--primary-color), 10%);
}

.input {
    width: 100%;
    padding: 10px;
    border: 1px solid #E0E0E0;
    border-radius: var(--border-radius);
}

.textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #E0E0E0;
    border-radius: var(--border-radius);
}

.select {
    width: 100%;
    padding: 10px;
    border: 1px solid #E0E0E0;
    border-radius: var(--border-radius);
}

.card {
    background-color: white;
    padding: 20px;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
}