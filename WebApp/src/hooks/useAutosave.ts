import { useCallback, useEffect, useRef, useState } from 'react';
import { UserDataService } from '../lib/services/userDataService';
import { SavedUserData } from '../lib/types/template';

interface UseAutosaveOptions {
  templateId: string;
  data: SavedUserData;
  enabled?: boolean;
  interval?: number; // milliseconds
  onSave?: (data: SavedUserData) => void;
  onError?: (error: Error) => void;
}

interface UseAutosaveReturn {
  isSaving: boolean;
  lastSaved: Date | null;
  saveNow: () => Promise<void>;
  hasUnsavedChanges: boolean;
}

export function useAutosave({
  templateId,
  data,
  enabled = true,
  interval = 3000, // 3 seconds default
  onSave,
  onError
}: UseAutosaveOptions): UseAutosaveReturn {
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  
  const lastDataRef = useRef<string>('');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isInitialLoad = useRef(true);

  // Function to save data
  const saveData = useCallback(async (dataToSave: SavedUserData): Promise<void> => {
    if (!templateId || isSaving) return;

    try {
      setIsSaving(true);
      await UserDataService.saveUserData(templateId, dataToSave);

      const now = new Date();
      setLastSaved(now);
      setHasUnsavedChanges(false);

      onSave?.(dataToSave);
      console.log(`✅ Autosaved data for template ${templateId}`);
    } catch (error) {
      const err = error instanceof Error ? error : new Error('Autosave failed');
      onError?.(err);
      console.error('Autosave failed:', err);
    } finally {
      setIsSaving(false);
    }
  }, [templateId, isSaving, onSave, onError]);

  // Manual save function
  const saveNow = async (): Promise<void> => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    await saveData(data);
  };

  // Effect to handle autosave
  useEffect(() => {
    if (!enabled || !templateId) return;

    const currentDataString = JSON.stringify(data);
    
    // Skip initial load to avoid saving empty data
    if (isInitialLoad.current) {
      lastDataRef.current = currentDataString;
      isInitialLoad.current = false;
      return;
    }

    // Check if data has changed
    if (currentDataString !== lastDataRef.current) {
      setHasUnsavedChanges(true);
      lastDataRef.current = currentDataString;

      // Clear existing timeout
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      // Set new timeout for autosave
      timeoutRef.current = setTimeout(() => {
        saveData(data);
      }, interval);
    }

    // Cleanup function
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [data, enabled, templateId, interval, saveData]);

  // Load initial data on mount
  useEffect(() => {
    if (!templateId) return;

    const loadInitialData = async () => {
      try {
        const savedData = UserDataService.getUserData(templateId);
        if (savedData?.lastSaved) {
          setLastSaved(new Date(savedData.lastSaved));
        }
      } catch (error) {
        console.warn('Failed to load initial autosave data:', error);
      }
    };

    loadInitialData();
  }, [templateId]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return {
    isSaving,
    lastSaved,
    saveNow,
    hasUnsavedChanges
  };
}

// Hook for loading saved user data
export function useSavedUserData(templateId: string) {
  const [savedData, setSavedData] = useState<SavedUserData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!templateId) {
      setIsLoading(false);
      return;
    }

    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Try to load from server first
        const serverData = await UserDataService.loadFromServer(templateId);
        if (serverData) {
          setSavedData(serverData);
        } else {
          // Fallback to local storage
          const localData = UserDataService.getUserData(templateId);
          setSavedData(localData);
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to load saved data';
        setError(errorMessage);
        console.error('Failed to load saved user data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [templateId]);

  return { savedData, isLoading, error };
}

// Hook for autosave status display
export function useAutosaveStatus(templateId: string) {
  const [status, setStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [message, setMessage] = useState<string>('');

  const updateStatus = (newStatus: typeof status, newMessage: string = '') => {
    setStatus(newStatus);
    setMessage(newMessage);

    // Auto-clear success/error messages after 3 seconds
    if (newStatus === 'saved' || newStatus === 'error') {
      setTimeout(() => {
        setStatus('idle');
        setMessage('');
      }, 3000);
    }
  };

  return {
    status,
    message,
    updateStatus
  };
}
