import { useState, useEffect } from 'react';
import { Template } from '../lib/types/template'; // Assuming a Template type is defined in your types

const useTemplates = () => {
    const [templates, setTemplates] = useState<Template[]>([]);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        const fetchTemplates = async () => {
            try {
                const response = await fetch('/api/templates'); // Adjust the endpoint as necessary
                if (!response.ok) {
                    throw new Error('Failed to fetch templates');
                }
                const data = await response.json();
                setTemplates(data);
            } catch (err) {
                setError(err instanceof Error ? err.message : 'An error occurred');
            } finally {
                setLoading(false);
            }
        };

        fetchTemplates();
    }, []);

    return { templates, loading, error };
};

export default useTemplates;