[{"id": "df9e9ee0-6b45-4148-9142-56492a92d849", "name": "SOw gen", "markdown": "#### STATEMENT OF WORK\n\n#### PROJECT NAME: AZURE MIDDLEWARE \n\n#### INTEGRATION HYPHEN\n\nThis Statement of Work (\"SOW\") is issued pursuant to, made part of, and\ngoverned by the Master Services Agreement.\n\n**\\\nBY: QUANTUMRHINO, INC.** (the \"Vendor\"), a corporation organized and\nexisting under the laws of the State of Delaware.\n\n**\\\nAND BETWEEN:** **TOLL BROTHERS, Inc.** (the \"Client\").\n\n# PURPOSE\n\nTo identify and memorialize expected work product and services produced\nby QuantumRhino (\"Vendor\") for Toll Brothers, Inc. (\"Client\"). This\nScope of Work (\"SOW\") will outline specific projects, expected\ntimeframes, and estimated costs. This SOW constitutes a binding offer by\nand between the contractor and the client.\n\n# BACKGROUND\n\nQuantumRhino, an Arizona based company focused on process automation and\nimplementing, extending, connecting, and supporting Salesforce solutions\nacross multiple industries. With years of experience, proven job\nperformance, and excellent customer satisfaction, QuantumRhino has been\nable to deliver services that enable organizations to be more efficient\nand help them optimize organizational strategies and functions. By\ndrawing on its management team's years of delivering successful\nenterprise level solutions, QuantumRhino removes the inflexible\ntechnologies of the past and enables their clients to take advantage of\nnew ways to engage customers and employees.\n\n# QUANTUMRHINO PLEDGE\n\nOur Partnership Pledge is how we conduct business with our clients. The\ntenants of our pledge include:\n\n- We will practice honest, open, and real communication.\n\n- We will ensure timely and successful completion of projects.\n\n- We recognize that our client's reputation and credibility are at risk.\n\n- We will always act with integrity.\n\n- We recognize project engagements are dynamic and we commit to a\n  flexible, focused project approach; we will not add additional charges\n  for reasonable changes to the project scope.\n\n#  SERVICES PROMISED\n\nAll services listed below are expected to be completed to client\nsatisfaction on or before agreed upon due date. However, if a change is\nrequested, the due date and any additional significant costs shall be\nrenegotiated and agreed upon by both parties prior to the continuance of\nthis SOW.\n\n#  OVERVIEW\n\nThis Statement of Work (SOW) defines the scope, deliverables, and\nresponsibilities for the integration between Hyphen BuildPro (Hyphen)\nand JD Edwards EnterpriseOne (JDE) utilizing Microsoft Azure resources.\nThe Vendor will design, develop, and implement the necessary\ninfrastructure and application components to enable seamless data\nexchange between Hyphen and JDE systems.\n\n#  STATEMENT OF WORK\n\nThe scope of this project includes the following interfaces:\n\n## Inbound (Hyphen to JDE)\n\n- Backcharge\n\n- EPOs\n\n- Line-item completion\n\n- Milestone\n\n- MPO Item\n\n- Lien Release\n\n## Outbound (JDE to Hyphen)\n\n- Address Book\n\n- AP Checks\n\n- AP Vouchers\n\n- Community\n\n- Cost Code\n\n- JIO/CO Notify\n\n- Lot\n\n- Measurement PO\n\n- Purchase Order\n\n## Outbound Development\n\n### JDE Low-Volume Architecture\n\nThe Vendor will leverage event-driven architecture to enable seamless\nintegration between JD Edwards (JDE) and Hyphen via Azure for low-volume\ndata. The high-level integration flow begins with a JDE application\ntrigger that generates an event transaction record.\n\n**Low-Volume Transaction Types:**\n\n- Community\n\n- Lot\n\n- JIO/CO Notify\n\n- Address Book\n\n- Cost Codes\n\n**Polling Frequency**: Near real-time\n\n### \n\n### JDE Moderate-Volume Architecture\n\nThe Vendor will leverage standard interoperability to create data in Z\ntables for moderate-volume data that is processed more frequently. Valid\nrecords are then staged in custom outbound tables, which Azure polls\nregularly.\n\n**\\\nModerate-Volume Transaction types:**\n\n- Purchase Orders\n\n- Measurement PO\n\n**Polling Frequency**: Near real-time\n\n### JDE High-Volume Architecture\n\nThe Vendor will leverage batch UBEs in JDE to extract large volumes of\ndata from Accounts Payable (AP) system tables. These records are staged\nin separate custom tables, organized by process type. Azure polls these\ntables, transmits the data to Hyphen and then updates the JDE staging\ntables with the final transaction status to complete the processing\nlifecycle.\n\n**High-Volume Transaction types:**\n\n- AP Checks\n\n- AP Vendors\n\n**Polling Frequency**: Batch 1 time daily\n\n### Azure JDE Outbound Architecture\n\nThe JDE Outbound integration leverages Azure Functions to poll JDE\nstaging tables, publish events to Event Grid, and process messages\nthrough Service Bus Queue before delivering data to Hyphen via SOAP API.\nThis serverless architecture enables near real-time data synchronization\nwith comprehensive BAM logging via Turbo360. The solution will use an\nAzure SQL Database to support data hydration and transformation between\nsystems.\n\n## **Development -- Inbound**\n\n### JDE Architecture\n\nThe Vendor will leverage Azure to call process-specific JDE AIS\nOrchestrations to deliver data into staging tables. JDE UBEs will\nprocess the staged data into the appropriate JDE systems using either\nstandard or custom BSFNs. Azure then polls the staging records to\ndetermine final disposition and updates Turbo360 accordingly, ensuring\naccurate tracking and closure of inbound transactions.\n\n**Inbound Transaction types:**\n\n- Backcharge\n\n- EPOs\n\n- Line-Item Completion\n\n- Milestone\n\n- MPO Item\n\n- Lien Release\n\n**Polling Frequency**: Near real-time with the exception of Backcharge,\nLine-Item Completion and Milestone, which are batched daily.\n\n### Azure JDE Inbound Event Drive Architecture\n\nThe JDE Inbound Azure Integration service implements a serverless\npolling architecture on Azure that retrieves data from Hyphen\\'s SOAP\nAPI, processes it through Service Bus Queue, and delivers transformed\nmessages to JDE via Orchestrator REST endpoints. The solution splits\nbatch messages into individual transactions, while maintaining logging\nin Azure SQL Database for auditing and recovery. Comprehensive BAM\nlogging through Turbo360 provides end-to-end visibility across the\nentire integration workflow.\n\n### Azure JDE Inbound Event Drive w/ File IO Architecture (JIO/CO Notify)\n\nThe JIO/CO Notify integration implements a hybrid architecture\nleveraging Azure Functions and Logic Apps to facilitate document\nnotifications with file transfers between JDE and Hyphen. The solution\npolls JDE staging tables for notification events, uses Logic Apps with\nSMB and SFTP connectors to securely transfer blob files from on-premises\nservers to Hyphen\\'s SFTP environment, and maintains the event-driven\nworkflow through Event Grid for coordinating the process. Document\nmetadata and SFTP file locations are then delivered to Hyphen via SOAP\nAPI, with comprehensive BAM logging through Turbo360 providing\nend-to-end visibility of both the file transfer and notification\noperations.\n\n# ROLES AND RESPONSIBILITIES\n\n## The Vendor\n\n- Develop, unit test, and deploy all integration components as outlined\n  in the scope of work.\n\n- Ensure compliance with applicable security standards.\n\n- Provide documentation for implemented components, including\n  architecture diagrams and configuration details when development is\n  completed.\n\n- Conduct knowledge transfer sessions with the Clientʼs team for ongoing\n  maintenance and support.\n\n## The Client\n\n- Provide access to JDE and Hyphen systems required for development and\n  testing.\n\n- Validate and approve the translation table mappings.\n\n- Conduct Quality Assurance (QA) and User Acceptance Testing (UAT) to\n  ensure functional alignment with business requirements.\n\n- Assign personnel to manage translation table updates and event\n  monitoring.\n\n# TIMELINE & MILESTONES\n\nThe Vendor and Client will mutually agree on project milestones,\nensuring timely completion of key deliverables. The estimated timeline\nwill be outlined in a separate project schedule document.\n\n# ACCEPTANCE CRITERIA\n\nThe Client will accept the deliverables based on the following criteria:\n\n- Functional validation of translation tables and data transformations.\n\n- Successful end-to-end data flow between Hyphen and JDE.\n\n- No critical or high-severity defects in the final deployment.\n\n- Documentation and knowledge transfer sessions completed.\n\n# ASSUMPTIONS & CONSTRAINTS\n\nTo ensure the successful implementation of the integration and avoid\nscope creep, the following assumptions and constraints are established:\n\n## General Assumptions\n\n- The Client will provide timely access to required systems,\n  environments, and documentation for JDE and Hyphen.\n\n- The Vendor will develop the integration based on the Clientʼs current\n  JDE and Hyphen system configurations. Any modifications to these\n  systems that impact the integration will require a change request.\n\n- The Client will designate a primary point of contact for requirement\n  clarifications, issue resolution, and approvals.\n\n- All development and testing will be performed in a non-production\n  environment before deployment to production.\n\n- Environment configurations and promotions will be limited to Dev, QA,\n  and Production.\n\n- The Client is responsible for the validation and ongoing maintenance\n  of the integration unless otherwise specified by a separate support\n  contract.\n\n## Event Processing Assumptions\n\n- **Data Management**: Archiving historical transactions in JDE,\n  Turbo360 and Azure is outside the scope of this project.\n\n- **Transaction Management:** Turbo360 will be utilized for error\n  management, integration validation and reprocessing transactions.\n  Changes to data for reprocessing transactions will require updates to\n  the source system, and transactions will be reprocessed in Turbo360 or\n  re-queued from the source system.\n\n- **Event Reliability:** The Vendor will implement event-retry and\n  error-handling mechanisms within the integration, but he Client is\n  responsible for monitoring and resolving upstream/downstream system\n  failures that may impact event delivery.\n\n- **Duplicate Event Handling:** The Vendor will implement processing to\n  prevent duplication of events for the same source system transaction.\n\n- **Frequency:** Transaction frequency denotes polling interval and\n  doesn't guarantee destination delivery time frame.\n\n## Infrastructure & Security Assumptions\n\n- The Vendor will provision necessary Azure resources using\n  Infrastructure-as-Code (IaC); however, the Client is responsible for\n  ongoing cloud subscription costs and maintenance of the\n  infrastructure.\n\n- The Client will provide necessary firewall and database access\n  required for integration components to communicate securely.\n\n- The integration will adhere to the Clientʼs existing security\n  policies, including authentication and authorization mechanisms.\n\n- Any additional security requirements (e.g., encryption beyond standard\n  Azure-provided capabilities) will require a change request.\n\n## Testing & Acceptance Assumptions\n\n- The Vendor will perform unit and integration testing, but the Client\n  is responsible for conducting quality assurance, user acceptance\n  testing (UAT), and approving the final implementation.\n\n- The Client will provide sample test cases and data to validate\n  integration scenarios.\n\n- Performance and load testing are outside the scope of this project\n  unless explicitly requested and approved via a change request.\n\n## Operational & Maintenance Assumptions\n\n- The Vendor will provide documentation and knowledge transfer at the\n  end of the project, but ongoing maintenance and monitoring of the\n  integration will be the Clientʼs responsibility.\n\n- Any future enhancements, modifications, or additional features beyond\n  what is outlined in this SOW will require a separate engagement or\n  change request.\n\n- The Client is responsible for ensuring that Hyphen and JDE system\n  changes do not disrupt the integration. Any updates requiring\n  integration modifications will be treated as a change request.\n\n## Constraints\n\n- The integration solution will be limited to the agreed-upon data\n  formats, APIs, and business rules.\n\n- Any deviation will require a formal change request.\n\n- The Vendor will adhere to the agreed-upon project timeline; delays in\n  the Clientʼs approvals, testing, or system access may impact delivery\n  schedules.\n\n- The solution will be designed to handle expected transaction volumes\n  as communicated by the Client. Any increase in data load beyond\n  initial projections may require re-evaluation and optimization.\n\n# PROJECT COST\n\nThe service provider estimates a total of 5,909 hours at a rate of\n\\$200.00 per hour, totaling \\$1,181,800.\n\n# PAYMENTS\n\nInvoices will be sent out based upon noted payment schedule listed in\nthis SOW. Client agrees that all payments will be paid within 30-days of\nreceiving invoice. Failure to make payment on time may lead to an\nadditional finance charge of one and one-half percent (1½%) per month or\nthe highest amount allowed by law, whichever is less. Clients may make\npayment either by ACH payment or credit card.\n\n# PAYMENT SCHEDULE\n\nClient agrees to pay 50% of estimate up front prior to any work being\nstarted. An additional 25% will be paid when the SOW is 50% complete.\nThe remaining 25% will be paid at the completion of this SOW. If there\nis a change to the SOW, or the original estimate of the SOW has been\ndetermined to be too low, the remaining balance will be invoiced on the\nfinal bill. Any change to the total invoiced amount shall be agreed upon\nby both parties.\n\n**SERVICE ASSUMPTIONS**\n\n- Any required software tools would be a pass-through expense and must\n  be approved by Client in advance.\n\n- End-user documentation will not be created or provided by Contractor\n  unless agreed to prior to work starting or an amendment is added to\n  this SOW.\n\n- Appropriate resources will be available to assist Contractor and\n  expedite the service.\n\n- Contractor will primarily work remotely but will facilitate onsite\n  meetings and ad-hoc remote sessions as appropriate.\n\n- All relevant business and technical documentation will be available to\n  Contractor.\n\n- Both Contractor and Client team members will achieve the deadlines\n  agreed upon during weekly project meetings.\n\n- Client will manage 3^rd^ party providers to ensure timeliness and\n  accuracy of deliverables.\n\n- Pass-through expenses (i.e., out-of-state travel, shipping, printing,\n  etc.) would be paid by Client.\n\n- Client will provide Contractor a Salesforce Admin User Account and any\n  other required user accounts for the duration of this engagement.\n\n- Contractor reserves the right to suspend activities under this\n  Agreement if invoices become past due.\n\n- Contractor reserves the right to invoice the Client for meeting times\n  when the Client is absent and fails to provide the Consultant with a\n  minimum of four hours advanced cancellation notice.\n\n# \n\n# CONCLUSION\n\nWe appreciate the opportunity to earn your trust and business. We are\ncommitted to ensuring a smooth and successful experience, and know we\nhave the right team and skills to exceed your expectations. If you have\nany questions about this SOW, feel free to contact us at your\nconvenience.\n\nThank you again for the opportunity, and we're looking forward to\nworking with you.\n\n# SIGNATURES\n\nBy affixing your signature below, you are accepting the engagement\npricing, terms, and scope as outlined in this SOW. The parties hereto\nare each acting with proper authority by their respective companies.\n\n**IN WITNESS WHEREOF**, QuantumRhino and the Client have caused this\nAgreement to be executed as of the following Effective Date:\n\n  ---------------- --------------------- -- ---------------- ----------------------\n      **Company:** EXAMPLE                      **Company:** Toll Brothers, Inc\n\n         **Date:** EXAMPLE                         **Date:** EXAMPLE\n\n    **Signature:** EXAMPLE                    **Signature:** EXAMPLE\n\n   **Print Name:** Chris Sinkwitz            **Print Name:** EXAMPLE\n\n        **Title:** Chief Executive                **Title:** EXAMPLE\n                   Officer                                   \n  ---------------- --------------------- -- ---------------- ----------------------\n", "sections": [], "createdAt": "2025-06-20T00:03:17.546Z", "usageCount": 0, "savedFormData": {"clientName": "", "clientEmail": "", "clientCompany": "", "clientPhone": "", "clientAddress": "2717 Delaware Street S, Minneapolis, MN", "clientTitle": "Marketing Director", "clientDepartment": "", "projectName": "", "projectType": "Web Application Development", "startDate": "2025-06-19", "endDate": "2025-06-26", "duration": "7 days", "hourlyRate": "150", "estimatedHours": "", "totalBudget": "$10,000 - $25,000", "paymentSchedule": [], "depositAmount": "", "depositDueDate": "", "finalPaymentAmount": "", "finalPaymentDueDate": "", "projectDescription": "", "deliverables": ["Fully functional web application", "Mobile application (iOS/Android)", "Responsive website design"], "requirements": [], "milestones": ["Project kickoff and requirements gathering", "Design mockups and approval", "Development phase completion", "Testing and quality assurance", "Client review and feedback", "Final delivery and deployment"], "changeRequestProcess": "Change requests must be submitted in writing and will be subject to additional time and cost estimates.", "communicationPlan": "Weekly status meetings and email updates. Primary contact via email and scheduled calls.", "qualityAssurance": "Comprehensive testing including unit tests, integration tests, and user acceptance testing.", "supportAndMaintenance": "30 days of post-launch support included. Extended support available under separate agreement.", "intellectualProperty": "All custom work and intellectual property will transfer to client upon full payment.", "confidentiality": "Both parties agree to maintain confidentiality of proprietary information.", "terminationClause": "Either party may terminate with 30 days written notice. Payment due for work completed."}}, {"id": "20ff6899-01dc-49b6-b8ce-8c9c2f9f1834", "name": "Test for edison", "markdown": "#### STATEMENT OF WORK\n\n#### PROJECT NAME: AZURE MIDDLEWARE \n\n#### INTEGRATION HYPHEN\n\nThis Statement of Work (\"SOW\") is issued pursuant to, made part of, and\ngoverned by the Master Services Agreement.\n\n**\\\nBY: QUANTUMRHINO, INC.** (the \"Vendor\"), a corporation organized and\nexisting under the laws of the State of Delaware.\n\n**\\\nAND BETWEEN:** **TOLL BROTHERS, Inc.** (the \"Client\").\n\n# PURPOSE\n\nTo identify and memorialize expected work product and services produced\nby QuantumRhino (\"Vendor\") for Toll Brothers, Inc. (\"Client\"). This\nScope of Work (\"SOW\") will outline specific projects, expected\ntimeframes, and estimated costs. This SOW constitutes a binding offer by\nand between the contractor and the client.\n\n# BACKGROUND\n\nQuantumRhino, an Arizona based company focused on process automation and\nimplementing, extending, connecting, and supporting Salesforce solutions\nacross multiple industries. With years of experience, proven job\nperformance, and excellent customer satisfaction, QuantumRhino has been\nable to deliver services that enable organizations to be more efficient\nand help them optimize organizational strategies and functions. By\ndrawing on its management team's years of delivering successful\nenterprise level solutions, QuantumRhino removes the inflexible\ntechnologies of the past and enables their clients to take advantage of\nnew ways to engage customers and employees.\n\n# QUANTUMRHINO PLEDGE\n\nOur Partnership Pledge is how we conduct business with our clients. The\ntenants of our pledge include:\n\n- We will practice honest, open, and real communication.\n\n- We will ensure timely and successful completion of projects.\n\n- We recognize that our client's reputation and credibility are at risk.\n\n- We will always act with integrity.\n\n- We recognize project engagements are dynamic and we commit to a\n  flexible, focused project approach; we will not add additional charges\n  for reasonable changes to the project scope.\n\n#  SERVICES PROMISED\n\nAll services listed below are expected to be completed to client\nsatisfaction on or before agreed upon due date. However, if a change is\nrequested, the due date and any additional significant costs shall be\nrenegotiated and agreed upon by both parties prior to the continuance of\nthis SOW.\n\n#  OVERVIEW\n\nThis Statement of Work (SOW) defines the scope, deliverables, and\nresponsibilities for the integration between Hyphen BuildPro (Hyphen)\nand JD Edwards EnterpriseOne (JDE) utilizing Microsoft Azure resources.\nThe Vendor will design, develop, and implement the necessary\ninfrastructure and application components to enable seamless data\nexchange between Hyphen and JDE systems.\n\n#  STATEMENT OF WORK\n\nThe scope of this project includes the following interfaces:\n\n## Inbound (Hyphen to JDE)\n\n- Backcharge\n\n- EPOs\n\n- Line-item completion\n\n- Milestone\n\n- MPO Item\n\n- Lien Release\n\n## Outbound (JDE to Hyphen)\n\n- Address Book\n\n- AP Checks\n\n- AP Vouchers\n\n- Community\n\n- Cost Code\n\n- JIO/CO Notify\n\n- Lot\n\n- Measurement PO\n\n- Purchase Order\n\n## Outbound Development\n\n### JDE Low-Volume Architecture\n\nThe Vendor will leverage event-driven architecture to enable seamless\nintegration between JD Edwards (JDE) and Hyphen via Azure for low-volume\ndata. The high-level integration flow begins with a JDE application\ntrigger that generates an event transaction record.\n\n**Low-Volume Transaction Types:**\n\n- Community\n\n- Lot\n\n- JIO/CO Notify\n\n- Address Book\n\n- Cost Codes\n\n**Polling Frequency**: Near real-time\n\n### \n\n### JDE Moderate-Volume Architecture\n\nThe Vendor will leverage standard interoperability to create data in Z\ntables for moderate-volume data that is processed more frequently. Valid\nrecords are then staged in custom outbound tables, which Azure polls\nregularly.\n\n**\\\nModerate-Volume Transaction types:**\n\n- Purchase Orders\n\n- Measurement PO\n\n**Polling Frequency**: Near real-time\n\n### JDE High-Volume Architecture\n\nThe Vendor will leverage batch UBEs in JDE to extract large volumes of\ndata from Accounts Payable (AP) system tables. These records are staged\nin separate custom tables, organized by process type. Azure polls these\ntables, transmits the data to Hyphen and then updates the JDE staging\ntables with the final transaction status to complete the processing\nlifecycle.\n\n**High-Volume Transaction types:**\n\n- AP Checks\n\n- AP Vendors\n\n**Polling Frequency**: Batch 1 time daily\n\n### Azure JDE Outbound Architecture\n\nThe JDE Outbound integration leverages Azure Functions to poll JDE\nstaging tables, publish events to Event Grid, and process messages\nthrough Service Bus Queue before delivering data to Hyphen via SOAP API.\nThis serverless architecture enables near real-time data synchronization\nwith comprehensive BAM logging via Turbo360. The solution will use an\nAzure SQL Database to support data hydration and transformation between\nsystems.\n\n## **Development -- Inbound**\n\n### JDE Architecture\n\nThe Vendor will leverage Azure to call process-specific JDE AIS\nOrchestrations to deliver data into staging tables. JDE UBEs will\nprocess the staged data into the appropriate JDE systems using either\nstandard or custom BSFNs. Azure then polls the staging records to\ndetermine final disposition and updates Turbo360 accordingly, ensuring\naccurate tracking and closure of inbound transactions.\n\n**Inbound Transaction types:**\n\n- Backcharge\n\n- EPOs\n\n- Line-Item Completion\n\n- Milestone\n\n- MPO Item\n\n- Lien Release\n\n**Polling Frequency**: Near real-time with the exception of Backcharge,\nLine-Item Completion and Milestone, which are batched daily.\n\n### Azure JDE Inbound Event Drive Architecture\n\nThe JDE Inbound Azure Integration service implements a serverless\npolling architecture on Azure that retrieves data from Hyphen\\'s SOAP\nAPI, processes it through Service Bus Queue, and delivers transformed\nmessages to JDE via Orchestrator REST endpoints. The solution splits\nbatch messages into individual transactions, while maintaining logging\nin Azure SQL Database for auditing and recovery. Comprehensive BAM\nlogging through Turbo360 provides end-to-end visibility across the\nentire integration workflow.\n\n### Azure JDE Inbound Event Drive w/ File IO Architecture (JIO/CO Notify)\n\nThe JIO/CO Notify integration implements a hybrid architecture\nleveraging Azure Functions and Logic Apps to facilitate document\nnotifications with file transfers between JDE and Hyphen. The solution\npolls JDE staging tables for notification events, uses Logic Apps with\nSMB and SFTP connectors to securely transfer blob files from on-premises\nservers to Hyphen\\'s SFTP environment, and maintains the event-driven\nworkflow through Event Grid for coordinating the process. Document\nmetadata and SFTP file locations are then delivered to Hyphen via SOAP\nAPI, with comprehensive BAM logging through Turbo360 providing\nend-to-end visibility of both the file transfer and notification\noperations.\n\n# ROLES AND RESPONSIBILITIES\n\n## The Vendor\n\n- Develop, unit test, and deploy all integration components as outlined\n  in the scope of work.\n\n- Ensure compliance with applicable security standards.\n\n- Provide documentation for implemented components, including\n  architecture diagrams and configuration details when development is\n  completed.\n\n- Conduct knowledge transfer sessions with the Clientʼs team for ongoing\n  maintenance and support.\n\n## The Client\n\n- Provide access to JDE and Hyphen systems required for development and\n  testing.\n\n- Validate and approve the translation table mappings.\n\n- Conduct Quality Assurance (QA) and User Acceptance Testing (UAT) to\n  ensure functional alignment with business requirements.\n\n- Assign personnel to manage translation table updates and event\n  monitoring.\n\n# TIMELINE & MILESTONES\n\nThe Vendor and Client will mutually agree on project milestones,\nensuring timely completion of key deliverables. The estimated timeline\nwill be outlined in a separate project schedule document.\n\n# ACCEPTANCE CRITERIA\n\nThe Client will accept the deliverables based on the following criteria:\n\n- Functional validation of translation tables and data transformations.\n\n- Successful end-to-end data flow between Hyphen and JDE.\n\n- No critical or high-severity defects in the final deployment.\n\n- Documentation and knowledge transfer sessions completed.\n\n# ASSUMPTIONS & CONSTRAINTS\n\nTo ensure the successful implementation of the integration and avoid\nscope creep, the following assumptions and constraints are established:\n\n## General Assumptions\n\n- The Client will provide timely access to required systems,\n  environments, and documentation for JDE and Hyphen.\n\n- The Vendor will develop the integration based on the Clientʼs current\n  JDE and Hyphen system configurations. Any modifications to these\n  systems that impact the integration will require a change request.\n\n- The Client will designate a primary point of contact for requirement\n  clarifications, issue resolution, and approvals.\n\n- All development and testing will be performed in a non-production\n  environment before deployment to production.\n\n- Environment configurations and promotions will be limited to Dev, QA,\n  and Production.\n\n- The Client is responsible for the validation and ongoing maintenance\n  of the integration unless otherwise specified by a separate support\n  contract.\n\n## Event Processing Assumptions\n\n- **Data Management**: Archiving historical transactions in JDE,\n  Turbo360 and Azure is outside the scope of this project.\n\n- **Transaction Management:** Turbo360 will be utilized for error\n  management, integration validation and reprocessing transactions.\n  Changes to data for reprocessing transactions will require updates to\n  the source system, and transactions will be reprocessed in Turbo360 or\n  re-queued from the source system.\n\n- **Event Reliability:** The Vendor will implement event-retry and\n  error-handling mechanisms within the integration, but he Client is\n  responsible for monitoring and resolving upstream/downstream system\n  failures that may impact event delivery.\n\n- **Duplicate Event Handling:** The Vendor will implement processing to\n  prevent duplication of events for the same source system transaction.\n\n- **Frequency:** Transaction frequency denotes polling interval and\n  doesn't guarantee destination delivery time frame.\n\n## Infrastructure & Security Assumptions\n\n- The Vendor will provision necessary Azure resources using\n  Infrastructure-as-Code (IaC); however, the Client is responsible for\n  ongoing cloud subscription costs and maintenance of the\n  infrastructure.\n\n- The Client will provide necessary firewall and database access\n  required for integration components to communicate securely.\n\n- The integration will adhere to the Clientʼs existing security\n  policies, including authentication and authorization mechanisms.\n\n- Any additional security requirements (e.g., encryption beyond standard\n  Azure-provided capabilities) will require a change request.\n\n## Testing & Acceptance Assumptions\n\n- The Vendor will perform unit and integration testing, but the Client\n  is responsible for conducting quality assurance, user acceptance\n  testing (UAT), and approving the final implementation.\n\n- The Client will provide sample test cases and data to validate\n  integration scenarios.\n\n- Performance and load testing are outside the scope of this project\n  unless explicitly requested and approved via a change request.\n\n## Operational & Maintenance Assumptions\n\n- The Vendor will provide documentation and knowledge transfer at the\n  end of the project, but ongoing maintenance and monitoring of the\n  integration will be the Clientʼs responsibility.\n\n- Any future enhancements, modifications, or additional features beyond\n  what is outlined in this SOW will require a separate engagement or\n  change request.\n\n- The Client is responsible for ensuring that Hyphen and JDE system\n  changes do not disrupt the integration. Any updates requiring\n  integration modifications will be treated as a change request.\n\n## Constraints\n\n- The integration solution will be limited to the agreed-upon data\n  formats, APIs, and business rules.\n\n- Any deviation will require a formal change request.\n\n- The Vendor will adhere to the agreed-upon project timeline; delays in\n  the Clientʼs approvals, testing, or system access may impact delivery\n  schedules.\n\n- The solution will be designed to handle expected transaction volumes\n  as communicated by the Client. Any increase in data load beyond\n  initial projections may require re-evaluation and optimization.\n\n# PROJECT COST\n\nThe service provider estimates a total of 5,909 hours at a rate of\n\\$200.00 per hour, totaling \\$1,181,800.\n\n# PAYMENTS\n\nInvoices will be sent out based upon noted payment schedule listed in\nthis SOW. Client agrees that all payments will be paid within 30-days of\nreceiving invoice. Failure to make payment on time may lead to an\nadditional finance charge of one and one-half percent (1½%) per month or\nthe highest amount allowed by law, whichever is less. Clients may make\npayment either by ACH payment or credit card.\n\n# PAYMENT SCHEDULE\n\nClient agrees to pay 50% of estimate up front prior to any work being\nstarted. An additional 25% will be paid when the SOW is 50% complete.\nThe remaining 25% will be paid at the completion of this SOW. If there\nis a change to the SOW, or the original estimate of the SOW has been\ndetermined to be too low, the remaining balance will be invoiced on the\nfinal bill. Any change to the total invoiced amount shall be agreed upon\nby both parties.\n\n**SERVICE ASSUMPTIONS**\n\n- Any required software tools would be a pass-through expense and must\n  be approved by Client in advance.\n\n- End-user documentation will not be created or provided by Contractor\n  unless agreed to prior to work starting or an amendment is added to\n  this SOW.\n\n- Appropriate resources will be available to assist Contractor and\n  expedite the service.\n\n- Contractor will primarily work remotely but will facilitate onsite\n  meetings and ad-hoc remote sessions as appropriate.\n\n- All relevant business and technical documentation will be available to\n  Contractor.\n\n- Both Contractor and Client team members will achieve the deadlines\n  agreed upon during weekly project meetings.\n\n- Client will manage 3^rd^ party providers to ensure timeliness and\n  accuracy of deliverables.\n\n- Pass-through expenses (i.e., out-of-state travel, shipping, printing,\n  etc.) would be paid by Client.\n\n- Client will provide Contractor a Salesforce Admin User Account and any\n  other required user accounts for the duration of this engagement.\n\n- Contractor reserves the right to suspend activities under this\n  Agreement if invoices become past due.\n\n- Contractor reserves the right to invoice the Client for meeting times\n  when the Client is absent and fails to provide the Consultant with a\n  minimum of four hours advanced cancellation notice.\n\n# \n\n# CONCLUSION\n\nWe appreciate the opportunity to earn your trust and business. We are\ncommitted to ensuring a smooth and successful experience, and know we\nhave the right team and skills to exceed your expectations. If you have\nany questions about this SOW, feel free to contact us at your\nconvenience.\n\nThank you again for the opportunity, and we're looking forward to\nworking with you.\n\n# SIGNATURES\n\nBy affixing your signature below, you are accepting the engagement\npricing, terms, and scope as outlined in this SOW. The parties hereto\nare each acting with proper authority by their respective companies.\n\n**IN WITNESS WHEREOF**, QuantumRhino and the Client have caused this\nAgreement to be executed as of the following Effective Date:\n\n  ---------------- --------------------- -- ---------------- ----------------------\n      **Company:** EXAMPLE                      **Company:** Toll Brothers, Inc\n\n         **Date:** EXAMPLE                         **Date:** EXAMPLE\n\n    **Signature:** EXAMPLE                    **Signature:** EXAMPLE\n\n   **Print Name:** Chris Sinkwitz            **Print Name:** EXAMPLE\n\n        **Title:** Chief Executive                **Title:** EXAMPLE\n                   Officer                                   \n  ---------------- --------------------- -- ---------------- ----------------------\n", "sections": [], "createdAt": "2025-06-20T16:12:16.566Z", "usageCount": 0, "savedFormData": {"clientName": "<PERSON>", "clientEmail": "chase<PERSON><PERSON><PERSON><PERSON>@icloud.com", "clientCompany": "Acme Corp", "clientPhone": "16125161697", "clientAddress": "7121 France Ave S\nApt 416, Minneapolis, MN 55401", "clientTitle": "Marketing Director", "clientDepartment": "Edison", "projectName": "Edison test ", "projectType": "Edison test", "startDate": "2025-06-02", "endDate": "2025-06-20", "duration": "3 weeks", "hourlyRate": "150", "estimatedHours": "12", "totalBudget": "1800", "paymentSchedule": [{"id": "1750435898957", "description": "Initial Payment (40%)", "amount": "$720", "dueDate": "2025-06-20", "milestone": "Project Start"}, {"id": "1750435898958", "description": "Milestone Payment (30%)", "amount": "$540", "dueDate": "2025-07-20", "milestone": "Mid-Project Milestone"}, {"id": "1750435898959", "description": "Final Payment (30%)", "amount": "$540", "dueDate": "2025-06-20", "milestone": "Project Completion"}], "depositAmount": "", "depositDueDate": "", "finalPaymentAmount": "", "finalPaymentDueDate": "", "projectDescription": "N/A", "deliverables": ["Fully functional web application", "Mobile application (iOS/Android)", "Responsive website design"], "requirements": [], "milestones": ["Project kickoff and requirements gathering", "Design mockups and approval", "Development phase completion", "Testing and quality assurance", "Client review and feedback", "Final delivery and deployment"], "changeRequestProcess": "Change requests must be submitted in writing and will be subject to additional time and cost estimates.", "communicationPlan": "Weekly status meetings and email updates. Primary contact via email and scheduled calls.", "qualityAssurance": "Comprehensive testing including unit tests, integration tests, and user acceptance testing.", "supportAndMaintenance": "30 days of post-launch support included. Extended support available under separate agreement.", "intellectualProperty": "All custom work and intellectual property will transfer to client upon full payment.", "confidentiality": "Both parties agree to maintain confidentiality of proprietary information.", "terminationClause": "Either party may terminate with 30 days written notice. Payment due for work completed."}}, {"id": "73d8e7e4-5fd1-46cd-945a-2bfd304333c3", "name": "Autofill test", "markdown": "#### STATEMENT OF WORK\n\n#### PROJECT NAME: AZURE MIDDLEWARE \n\n#### INTEGRATION HYPHEN\n\nThis Statement of Work (\"SOW\") is issued pursuant to, made part of, and\ngoverned by the Master Services Agreement.\n\n**\\\nBY: QUANTUMRHINO, INC.** (the \"Vendor\"), a corporation organized and\nexisting under the laws of the State of Delaware.\n\n**\\\nAND BETWEEN:** **TOLL BROTHERS, Inc.** (the \"Client\").\n\n# PURPOSE\n\nTo identify and memorialize expected work product and services produced\nby QuantumRhino (\"Vendor\") for Toll Brothers, Inc. (\"Client\"). This\nScope of Work (\"SOW\") will outline specific projects, expected\ntimeframes, and estimated costs. This SOW constitutes a binding offer by\nand between the contractor and the client.\n\n# BACKGROUND\n\nQuantumRhino, an Arizona based company focused on process automation and\nimplementing, extending, connecting, and supporting Salesforce solutions\nacross multiple industries. With years of experience, proven job\nperformance, and excellent customer satisfaction, QuantumRhino has been\nable to deliver services that enable organizations to be more efficient\nand help them optimize organizational strategies and functions. By\ndrawing on its management team's years of delivering successful\nenterprise level solutions, QuantumRhino removes the inflexible\ntechnologies of the past and enables their clients to take advantage of\nnew ways to engage customers and employees.\n\n# QUANTUMRHINO PLEDGE\n\nOur Partnership Pledge is how we conduct business with our clients. The\ntenants of our pledge include:\n\n- We will practice honest, open, and real communication.\n\n- We will ensure timely and successful completion of projects.\n\n- We recognize that our client's reputation and credibility are at risk.\n\n- We will always act with integrity.\n\n- We recognize project engagements are dynamic and we commit to a\n  flexible, focused project approach; we will not add additional charges\n  for reasonable changes to the project scope.\n\n#  SERVICES PROMISED\n\nAll services listed below are expected to be completed to client\nsatisfaction on or before agreed upon due date. However, if a change is\nrequested, the due date and any additional significant costs shall be\nrenegotiated and agreed upon by both parties prior to the continuance of\nthis SOW.\n\n#  OVERVIEW\n\nThis Statement of Work (SOW) defines the scope, deliverables, and\nresponsibilities for the integration between Hyphen BuildPro (Hyphen)\nand JD Edwards EnterpriseOne (JDE) utilizing Microsoft Azure resources.\nThe Vendor will design, develop, and implement the necessary\ninfrastructure and application components to enable seamless data\nexchange between Hyphen and JDE systems.\n\n#  STATEMENT OF WORK\n\nThe scope of this project includes the following interfaces:\n\n## Inbound (Hyphen to JDE)\n\n- Backcharge\n\n- EPOs\n\n- Line-item completion\n\n- Milestone\n\n- MPO Item\n\n- Lien Release\n\n## Outbound (JDE to Hyphen)\n\n- Address Book\n\n- AP Checks\n\n- AP Vouchers\n\n- Community\n\n- Cost Code\n\n- JIO/CO Notify\n\n- Lot\n\n- Measurement PO\n\n- Purchase Order\n\n## Outbound Development\n\n### JDE Low-Volume Architecture\n\nThe Vendor will leverage event-driven architecture to enable seamless\nintegration between JD Edwards (JDE) and Hyphen via Azure for low-volume\ndata. The high-level integration flow begins with a JDE application\ntrigger that generates an event transaction record.\n\n**Low-Volume Transaction Types:**\n\n- Community\n\n- Lot\n\n- JIO/CO Notify\n\n- Address Book\n\n- Cost Codes\n\n**Polling Frequency**: Near real-time\n\n### \n\n### JDE Moderate-Volume Architecture\n\nThe Vendor will leverage standard interoperability to create data in Z\ntables for moderate-volume data that is processed more frequently. Valid\nrecords are then staged in custom outbound tables, which Azure polls\nregularly.\n\n**\\\nModerate-Volume Transaction types:**\n\n- Purchase Orders\n\n- Measurement PO\n\n**Polling Frequency**: Near real-time\n\n### JDE High-Volume Architecture\n\nThe Vendor will leverage batch UBEs in JDE to extract large volumes of\ndata from Accounts Payable (AP) system tables. These records are staged\nin separate custom tables, organized by process type. Azure polls these\ntables, transmits the data to Hyphen and then updates the JDE staging\ntables with the final transaction status to complete the processing\nlifecycle.\n\n**High-Volume Transaction types:**\n\n- AP Checks\n\n- AP Vendors\n\n**Polling Frequency**: Batch 1 time daily\n\n### Azure JDE Outbound Architecture\n\nThe JDE Outbound integration leverages Azure Functions to poll JDE\nstaging tables, publish events to Event Grid, and process messages\nthrough Service Bus Queue before delivering data to Hyphen via SOAP API.\nThis serverless architecture enables near real-time data synchronization\nwith comprehensive BAM logging via Turbo360. The solution will use an\nAzure SQL Database to support data hydration and transformation between\nsystems.\n\n## **Development -- Inbound**\n\n### JDE Architecture\n\nThe Vendor will leverage Azure to call process-specific JDE AIS\nOrchestrations to deliver data into staging tables. JDE UBEs will\nprocess the staged data into the appropriate JDE systems using either\nstandard or custom BSFNs. Azure then polls the staging records to\ndetermine final disposition and updates Turbo360 accordingly, ensuring\naccurate tracking and closure of inbound transactions.\n\n**Inbound Transaction types:**\n\n- Backcharge\n\n- EPOs\n\n- Line-Item Completion\n\n- Milestone\n\n- MPO Item\n\n- Lien Release\n\n**Polling Frequency**: Near real-time with the exception of Backcharge,\nLine-Item Completion and Milestone, which are batched daily.\n\n### Azure JDE Inbound Event Drive Architecture\n\nThe JDE Inbound Azure Integration service implements a serverless\npolling architecture on Azure that retrieves data from Hyphen\\'s SOAP\nAPI, processes it through Service Bus Queue, and delivers transformed\nmessages to JDE via Orchestrator REST endpoints. The solution splits\nbatch messages into individual transactions, while maintaining logging\nin Azure SQL Database for auditing and recovery. Comprehensive BAM\nlogging through Turbo360 provides end-to-end visibility across the\nentire integration workflow.\n\n### Azure JDE Inbound Event Drive w/ File IO Architecture (JIO/CO Notify)\n\nThe JIO/CO Notify integration implements a hybrid architecture\nleveraging Azure Functions and Logic Apps to facilitate document\nnotifications with file transfers between JDE and Hyphen. The solution\npolls JDE staging tables for notification events, uses Logic Apps with\nSMB and SFTP connectors to securely transfer blob files from on-premises\nservers to Hyphen\\'s SFTP environment, and maintains the event-driven\nworkflow through Event Grid for coordinating the process. Document\nmetadata and SFTP file locations are then delivered to Hyphen via SOAP\nAPI, with comprehensive BAM logging through Turbo360 providing\nend-to-end visibility of both the file transfer and notification\noperations.\n\n# ROLES AND RESPONSIBILITIES\n\n## The Vendor\n\n- Develop, unit test, and deploy all integration components as outlined\n  in the scope of work.\n\n- Ensure compliance with applicable security standards.\n\n- Provide documentation for implemented components, including\n  architecture diagrams and configuration details when development is\n  completed.\n\n- Conduct knowledge transfer sessions with the Clientʼs team for ongoing\n  maintenance and support.\n\n## The Client\n\n- Provide access to JDE and Hyphen systems required for development and\n  testing.\n\n- Validate and approve the translation table mappings.\n\n- Conduct Quality Assurance (QA) and User Acceptance Testing (UAT) to\n  ensure functional alignment with business requirements.\n\n- Assign personnel to manage translation table updates and event\n  monitoring.\n\n# TIMELINE & MILESTONES\n\nThe Vendor and Client will mutually agree on project milestones,\nensuring timely completion of key deliverables. The estimated timeline\nwill be outlined in a separate project schedule document.\n\n# ACCEPTANCE CRITERIA\n\nThe Client will accept the deliverables based on the following criteria:\n\n- Functional validation of translation tables and data transformations.\n\n- Successful end-to-end data flow between Hyphen and JDE.\n\n- No critical or high-severity defects in the final deployment.\n\n- Documentation and knowledge transfer sessions completed.\n\n# ASSUMPTIONS & CONSTRAINTS\n\nTo ensure the successful implementation of the integration and avoid\nscope creep, the following assumptions and constraints are established:\n\n## General Assumptions\n\n- The Client will provide timely access to required systems,\n  environments, and documentation for JDE and Hyphen.\n\n- The Vendor will develop the integration based on the Clientʼs current\n  JDE and Hyphen system configurations. Any modifications to these\n  systems that impact the integration will require a change request.\n\n- The Client will designate a primary point of contact for requirement\n  clarifications, issue resolution, and approvals.\n\n- All development and testing will be performed in a non-production\n  environment before deployment to production.\n\n- Environment configurations and promotions will be limited to Dev, QA,\n  and Production.\n\n- The Client is responsible for the validation and ongoing maintenance\n  of the integration unless otherwise specified by a separate support\n  contract.\n\n## Event Processing Assumptions\n\n- **Data Management**: Archiving historical transactions in JDE,\n  Turbo360 and Azure is outside the scope of this project.\n\n- **Transaction Management:** Turbo360 will be utilized for error\n  management, integration validation and reprocessing transactions.\n  Changes to data for reprocessing transactions will require updates to\n  the source system, and transactions will be reprocessed in Turbo360 or\n  re-queued from the source system.\n\n- **Event Reliability:** The Vendor will implement event-retry and\n  error-handling mechanisms within the integration, but he Client is\n  responsible for monitoring and resolving upstream/downstream system\n  failures that may impact event delivery.\n\n- **Duplicate Event Handling:** The Vendor will implement processing to\n  prevent duplication of events for the same source system transaction.\n\n- **Frequency:** Transaction frequency denotes polling interval and\n  doesn't guarantee destination delivery time frame.\n\n## Infrastructure & Security Assumptions\n\n- The Vendor will provision necessary Azure resources using\n  Infrastructure-as-Code (IaC); however, the Client is responsible for\n  ongoing cloud subscription costs and maintenance of the\n  infrastructure.\n\n- The Client will provide necessary firewall and database access\n  required for integration components to communicate securely.\n\n- The integration will adhere to the Clientʼs existing security\n  policies, including authentication and authorization mechanisms.\n\n- Any additional security requirements (e.g., encryption beyond standard\n  Azure-provided capabilities) will require a change request.\n\n## Testing & Acceptance Assumptions\n\n- The Vendor will perform unit and integration testing, but the Client\n  is responsible for conducting quality assurance, user acceptance\n  testing (UAT), and approving the final implementation.\n\n- The Client will provide sample test cases and data to validate\n  integration scenarios.\n\n- Performance and load testing are outside the scope of this project\n  unless explicitly requested and approved via a change request.\n\n## Operational & Maintenance Assumptions\n\n- The Vendor will provide documentation and knowledge transfer at the\n  end of the project, but ongoing maintenance and monitoring of the\n  integration will be the Clientʼs responsibility.\n\n- Any future enhancements, modifications, or additional features beyond\n  what is outlined in this SOW will require a separate engagement or\n  change request.\n\n- The Client is responsible for ensuring that Hyphen and JDE system\n  changes do not disrupt the integration. Any updates requiring\n  integration modifications will be treated as a change request.\n\n## Constraints\n\n- The integration solution will be limited to the agreed-upon data\n  formats, APIs, and business rules.\n\n- Any deviation will require a formal change request.\n\n- The Vendor will adhere to the agreed-upon project timeline; delays in\n  the Clientʼs approvals, testing, or system access may impact delivery\n  schedules.\n\n- The solution will be designed to handle expected transaction volumes\n  as communicated by the Client. Any increase in data load beyond\n  initial projections may require re-evaluation and optimization.\n\n# PROJECT COST\n\nThe service provider estimates a total of 5,909 hours at a rate of\n\\$200.00 per hour, totaling \\$1,181,800.\n\n# PAYMENTS\n\nInvoices will be sent out based upon noted payment schedule listed in\nthis SOW. Client agrees that all payments will be paid within 30-days of\nreceiving invoice. Failure to make payment on time may lead to an\nadditional finance charge of one and one-half percent (1½%) per month or\nthe highest amount allowed by law, whichever is less. Clients may make\npayment either by ACH payment or credit card.\n\n# PAYMENT SCHEDULE\n\nClient agrees to pay 50% of estimate up front prior to any work being\nstarted. An additional 25% will be paid when the SOW is 50% complete.\nThe remaining 25% will be paid at the completion of this SOW. If there\nis a change to the SOW, or the original estimate of the SOW has been\ndetermined to be too low, the remaining balance will be invoiced on the\nfinal bill. Any change to the total invoiced amount shall be agreed upon\nby both parties.\n\n**SERVICE ASSUMPTIONS**\n\n- Any required software tools would be a pass-through expense and must\n  be approved by Client in advance.\n\n- End-user documentation will not be created or provided by Contractor\n  unless agreed to prior to work starting or an amendment is added to\n  this SOW.\n\n- Appropriate resources will be available to assist Contractor and\n  expedite the service.\n\n- Contractor will primarily work remotely but will facilitate onsite\n  meetings and ad-hoc remote sessions as appropriate.\n\n- All relevant business and technical documentation will be available to\n  Contractor.\n\n- Both Contractor and Client team members will achieve the deadlines\n  agreed upon during weekly project meetings.\n\n- Client will manage 3^rd^ party providers to ensure timeliness and\n  accuracy of deliverables.\n\n- Pass-through expenses (i.e., out-of-state travel, shipping, printing,\n  etc.) would be paid by Client.\n\n- Client will provide Contractor a Salesforce Admin User Account and any\n  other required user accounts for the duration of this engagement.\n\n- Contractor reserves the right to suspend activities under this\n  Agreement if invoices become past due.\n\n- Contractor reserves the right to invoice the Client for meeting times\n  when the Client is absent and fails to provide the Consultant with a\n  minimum of four hours advanced cancellation notice.\n\n# \n\n# CONCLUSION\n\nWe appreciate the opportunity to earn your trust and business. We are\ncommitted to ensuring a smooth and successful experience, and know we\nhave the right team and skills to exceed your expectations. If you have\nany questions about this SOW, feel free to contact us at your\nconvenience.\n\nThank you again for the opportunity, and we're looking forward to\nworking with you.\n\n# SIGNATURES\n\nBy affixing your signature below, you are accepting the engagement\npricing, terms, and scope as outlined in this SOW. The parties hereto\nare each acting with proper authority by their respective companies.\n\n**IN WITNESS WHEREOF**, QuantumRhino and the Client have caused this\nAgreement to be executed as of the following Effective Date:\n\n  ---------------- --------------------- -- ---------------- ----------------------\n      **Company:** EXAMPLE                      **Company:** Toll Brothers, Inc\n\n         **Date:** EXAMPLE                         **Date:** EXAMPLE\n\n    **Signature:** EXAMPLE                    **Signature:** EXAMPLE\n\n   **Print Name:** Chris Sinkwitz            **Print Name:** EXAMPLE\n\n        **Title:** Chief Executive                **Title:** EXAMPLE\n                   Officer                                   \n  ---------------- --------------------- -- ---------------- ----------------------\n", "sections": [], "createdAt": "2025-06-23T20:21:10.328Z", "usageCount": 0, "savedFormData": {"clientName": "", "clientEmail": "", "clientCompany": "", "clientPhone": "", "clientAddress": "", "clientTitle": "", "clientDepartment": "", "projectName": "Acme c", "projectType": "Web Application Development", "startDate": "2025-06-23", "endDate": "2025-07-23", "duration": "5 weeks", "hourlyRate": "150", "estimatedHours": "", "totalBudget": "$10,000 - $25,000", "userCompanyName": "", "userContactName": "", "userEmail": "", "userPhone": "", "userAddress": "", "userTitle": "", "userDepartment": "", "paymentSchedule": [], "depositAmount": "", "depositDueDate": "", "finalPaymentAmount": "", "finalPaymentDueDate": "", "projectDescription": "", "deliverables": ["Fully functional web application", "Mobile application (iOS/Android)", "Responsive website design"], "requirements": [], "milestones": ["Project kickoff and requirements gathering", "Design mockups and approval", "Development phase completion", "Testing and quality assurance", "Client review and feedback", "Final delivery and deployment"], "changeRequestProcess": "Change requests must be submitted in writing and will be subject to additional time and cost estimates.", "communicationPlan": "Weekly status meetings and email updates. Primary contact via email and scheduled calls.", "qualityAssurance": "Comprehensive testing including unit tests, integration tests, and user acceptance testing.", "supportAndMaintenance": "30 days of post-launch support included. Extended support available under separate agreement.", "intellectualProperty": "All custom work and intellectual property will transfer to client upon full payment.", "confidentiality": "Both parties agree to maintain confidentiality of proprietary information.", "terminationClause": "Either party may terminate with 30 days written notice. Payment due for work completed."}}]