// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Company {
  id        Int      @id @default(autoincrement())
  name      String
  address   String?
  email     String?
  phone     String?
  sows      SOW[]
}

model SOW {
  id                Int      @id @default(autoincrement())
  projectName      String
  clientName       String
  vendorName       String
  scopeOfWork      String
  timeline         String
  pricing          String
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt
  companyId        Int
  company          Company  @relation(fields: [companyId], references: [id])
}