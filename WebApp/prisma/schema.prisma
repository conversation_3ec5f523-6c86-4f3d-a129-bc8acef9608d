// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model for authentication and ownership
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  avatar        String?
  role          UserRole  @default(USER)
  organizationId String?
  organization  Organization? @relation(fields: [organizationId], references: [id])

  // SOW Generator specific fields
  templates     Template[]
  sows          SOW[]
  companies     Company[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("users")
}

model Organization {
  id          String   @id @default(cuid())
  name        String
  domain      String?
  users       User[]

  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("organizations")
}

enum UserRole {
  SUPER_ADMIN
  ORG_ADMIN
  USER
  VIEWER
}

// Template model for user-specific templates
model Template {
  id            String    @id @default(cuid())
  name          String
  description   String?
  filePath      String    // Path to template file in volume storage
  fileName      String    // Original filename
  fileSize      Int       // File size in bytes
  mimeType      String    // MIME type
  isDefault     Boolean   @default(false)
  isActive      Boolean   @default(true)

  // User ownership
  userId        String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Usage tracking
  sows          SOW[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("templates")
}

// Company model for client information
model Company {
  id            String    @id @default(cuid())
  name          String
  address       String?
  email         String?
  phone         String?
  website       String?
  contactPerson String?

  // User ownership
  userId        String
  user          User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Related SOWs
  sows          SOW[]

  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  @@map("companies")
}

// SOW model for generated statements of work
model SOW {
  id                String      @id @default(cuid())
  projectName       String
  clientName        String
  vendorName        String
  scopeOfWork       String      @db.Text
  timeline          String      @db.Text
  pricing           String      @db.Text

  // File information
  filePath          String?     // Path to generated SOW file
  fileName          String?     // Generated filename
  fileSize          Int?        // File size in bytes

  // Template used
  templateId        String?
  template          Template?   @relation(fields: [templateId], references: [id])

  // Company/Client
  companyId         String?
  company           Company?    @relation(fields: [companyId], references: [id])

  // User ownership
  userId            String
  user              User        @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Status and metadata
  status            SOWStatus   @default(DRAFT)
  version           Int         @default(1)

  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt

  @@map("sows")
}

enum SOWStatus {
  DRAFT
  REVIEW
  APPROVED
  SENT
  SIGNED
  COMPLETED
}