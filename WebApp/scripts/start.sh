#!/bin/bash

echo "🚀 Starting SOW Generator..."

# Initialize storage directories
echo "📁 Initializing storage directories..."
mkdir -p /app/storage/templates
mkdir -p /app/storage/sows
mkdir -p /app/storage/uploads
echo "✅ Storage directories created"

# Try to push database schema
echo "🗄️ Setting up database..."
if npx prisma db push --accept-data-loss; then
    echo "✅ Database schema updated successfully"
else
    echo "⚠️ Database schema push failed, but continuing..."
    echo "This might be normal for the first deployment"
fi

# Generate Prisma client (just in case)
echo "🔧 Generating Prisma client..."
npx prisma generate

# Start the application
echo "🌟 Starting Next.js application..."
exec npm start
