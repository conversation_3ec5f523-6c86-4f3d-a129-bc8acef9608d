{"name": "quantumrhino-sow-generator", "version": "1.0.0", "description": "Professional Statement of Work generator for QuantumRhino - streamlining SOW creation for software development projects.", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:migrate": "prisma migrate deploy", "db:push": "prisma db push", "db:studio": "prisma studio", "postinstall": "prisma generate"}, "dependencies": {"@google/generative-ai": "^0.24.1", "@prisma/client": "^5.22.0", "@types/uuid": "^10.0.0", "docx": "^9.5.0", "docx-preview": "^0.3.5", "docx-templates": "^4.14.1", "next": "^15.3.3", "pizzip": "^3.2.0", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0", "multer": "^1.4.5-lts.1", "fs-extra": "^11.2.0", "path": "^0.12.7", "mime-types": "^2.1.35", "archiver": "^7.0.1", "sharp": "^0.33.5"}, "devDependencies": {"@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@types/multer": "^1.4.12", "@types/fs-extra": "^11.0.4", "@types/mime-types": "^2.1.4", "@types/archiver": "^6.0.2", "autoprefixer": "^10.4.21", "eslint": "^8.57.1", "eslint-config-next": "^15.3.3", "postcss": "^8.5.4", "prisma": "^5.22.0", "tailwindcss": "^3.4.0", "typescript": "^5.8.3"}, "keywords": ["SOW", "Statement of Work", "QuantumRhino", "software development", "web application", "document generation"], "author": "QuantumRhino", "license": "MIT"}